# Generated by Django 4.2.5 on 2023-11-09 13:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0020_appointments_dateoffixingapp_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientDetails',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Address', models.CharField(blank=True, max_length=100, null=True)),
                ('Height', models.CharField(blank=True, max_length=50, null=True)),
                ('Weight', models.CharField(blank=True, max_length=50, null=True)),
                ('DietaryRestrictions', models.TextField(blank=True, null=True)),
                ('Allergies', models.CharField(blank=True, max_length=100, null=True)),
                ('ExistingIllness', models.TextField(blank=True, null=True)),
                ('PastIllness', models.TextField(blank=True, null=True)),
                ('Notes', models.TextField(blank=True, null=True)),
                ('TimeZone', models.CharField(blank=True, max_length=100, null=True)),
                ('PatientId', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
