from python_http_client.exceptions import HTTPError
import requests
from rest_framework.response import Response
import os
import json
import requests
import logging


# def generate_response(success, message=None, status_code, data=None):
def generate_response(success, status_code, message=None, data=None):
    response_data = {"isSuccess": success}

    if message is not None:
        response_data["message"] = message

    if data is not None:
        response_data["data"] = data

    return Response(response_data, status=status_code)
    # return Response({"isSuccess": success, "message": message, "data": data}, status_code)

"""DEPRICIATED"""
def send_email(recipient_email, recipient_name, url, template_key):
    """Send an email using ZeptoMail API."""
    try:
        zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"

        payload = {
            "template_key": template_key,
            "from": {"address": "<EMAIL>", "name": "Health Unwired"},
            "to": [{"email_address": {"address": recipient_email, "name": recipient_name}}],
            "merge_info": {
                "u_name": recipient_name,
                "u_email": recipient_email,
                "verify_url1": url,
                "fb_url":os.getenv("fb_url"),
                "insta_url":os.getenv("insta_url"),
                "twitter_url":os.getenv("twitter_url"),
                "linkedin_url":os.getenv("linkedin_url"),
                "youtube_url":os.getenv("youtube_url"),
            },
        }

        headers = {
            "Authorization": os.getenv("ZEPTOMAIL_TOKEN"),
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        response = requests.post(
            zeptomail_url, headers=headers, data=json.dumps(payload))

        # Print response details for debugging
        print(f"ZeptoMail Response: {response.status_code} - {response.text}")

        # Log the error for debugging
        if response.status_code != 200:
            logging.error(
                f"Error sending email: {response.status_code} - {response.text}")

        response.raise_for_status()
        return response.status_code == 200

    except requests.exceptions.RequestException as e:
        logging.error(f"Exception occurred while sending email: {e}")
        return False
