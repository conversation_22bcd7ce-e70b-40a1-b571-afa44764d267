# Generated by Django 4.2.5 on 2023-11-03 14:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0014_testjson'),
    ]

    operations = [
        migrations.CreateModel(
            name='DoctorDeta<PERSON>',
            fields=[
                ('DoctorId', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('Address', models.CharField(blank=True, max_length=100, null=True)),
                ('Languages', models.CharField(blank=True, max_length=50, null=True)),
                ('Summary', models.TextField()),
                ('PractisingHospital', models.CharField(blank=True, max_length=50, null=True)),
                ('MemberCode', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('Dept', models.CharField(blank=True, max_length=50, null=True)),
                ('Qualifications', models.JSONField(null=True)),
                ('Certificates', models.JSONField(null=True)),
                ('TimeZone', models.CharField(blank=True, max_length=20, null=True)),
                ('SocialLinks', models.JSONField(null=True)),
            ],
        ),
    ]
