import zoneinfo
from rest_framework_simplejwt import authentication
from django.utils import timezone
from django.urls import reverse
from django.http import JsonResponse, HttpResponse


class TimezoneMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):

        return self.get_response(request)

    def process_view(self, request, view_func, view_args, view_kwargs):

        prefixes = [
            "/api/payment-processing/",
            "/api/airwallex-webhook/",
            "/api/search_doctors/",
            "/api/precompute-embeddings/",
            "/api/login/",
            "/api/register/",
            "/api/social-user/",
            "/api/user-verify-otp/",
            "/api/user-resend-otp/",
            "/api/password_reset_link/",
            "/api/verify_password/",
            "/api/get_doctors_by_expertise/",
            "/media/",
            "/api/get-expertise/",
            "/api/get-doctor-details/",
            "/api/get-testimonials/",
            "/api/get-doctor-feedbacks/",
            "/api/token/refresh/",
            "/api/payment_events/",
            "/api/cancer-types-main/",
            "cancer-types-category/",
            "/api/submit-expert-review/",
            "/api/get-expert-blogs",
            "/api/get-expert-reviews",
            "/api/creowiztestmail/",
            "/api/get-experts-by-filter/",
            "/api/expert_profile_reactivation",
            "/api/get_app_policy_content_type/",
            "/swagger/",
            "/api/add_view/",
            "/api/get-create-faq/",
            "/api/get-single-blog/",
            "/api/get-single-podcast/",
            "/api/get-expert-podcast/",
            "/api/get-create-podcast-category/",
            "/api/get-create-blog-category/",
            "/api/get-blog-section/",
            "/api/get-podcast-section/",
            "/api/get-blog-podcast-section/",
            "/api/get-blog-category/",
            "/api/get-podcast-category/",
            "/api/get-top-authors/",
            "/api/get-banner/",
            "/api/get-random-expert/",
            "/api/get-common-topics/",
            "/api/get-select-content/",
            "/api/get-videos-library/",
            "/api/subscribe/",
            "/api/unsubscribe/",
            "/api/specialties/",


        ]

        if request.path.startswith(tuple(prefixes)):
            return None
        # added
        try:
            request.user = authentication.JWTAuthentication().authenticate(request)[
                0
            ]  # Manually authenticate the token
        except Exception as e:
            return HttpResponse("Invalid token", status=401)
        # added
        tzname = request.user.TimeZone

        if tzname:
            timezone.activate(zoneinfo.ZoneInfo(tzname))
            print(
                f"in middlewar{tzname}----------------{request.user}-------{timezone.get_current_timezone_name()}-----{timezone.now()}"
            )
        else:
            timezone.deactivate()
