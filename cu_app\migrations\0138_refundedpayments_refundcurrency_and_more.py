# Generated by Django 4.2.5 on 2024-06-26 11:26

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0137_refundedpayments_appid'),
    ]

    operations = [
        migrations.AddField(
            model_name='refundedpayments',
            name='RefundCurrency',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='refundedpayments',
            name='RefundDate',
            field=models.DateTimeField(default=datetime.datetime(2024, 6, 26, 11, 26, 47, 19725, tzinfo=datetime.timezone.utc)),
        ),
    ]
