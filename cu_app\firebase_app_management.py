from firebase_admin import initialize_app, get_app
from firebase_admin.exceptions import FirebaseError

FIREBASE_APP = None


def get_firebase_app():
    """
    Safely initializes or retrieves the Firebase app.
    Ensures initialize_app() is not called multiple times.
    """
    global FIREBASE_APP
    if not FIREBASE_APP:
        try:
            FIREBASE_APP = initialize_app()
        except FirebaseError:
            # If already initialized, get the existing app
            FIREBASE_APP = get_app()
    return FIREBASE_APP