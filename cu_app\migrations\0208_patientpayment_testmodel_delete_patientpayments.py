# Generated by Django 4.2.5 on 2025-05-06 15:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0207_remove_doctordetails_embedding_embedding'),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('PaymentIntent', models.CharField(blank=True, max_length=100, null=True)),
                ('CheckOutSessionId', models.TextField(blank=True, null=True)),
                ('amount', models.IntegerField(default=0)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('payment_status', models.Char<PERSON>ield(max_length=20)),
                ('AppointmentId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
                ('PatientId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='TestModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('test_field', models.CharField(blank=True, max_length=100, null=True)),
            ],
        ),
        migrations.DeleteModel(
            name='PatientPayments',
        ),
    ]
