from django.core.files.storage import FileSystemStorage
import boto3
from datetime import datetime,date, timedelta
import random
import os
from botocore.config import Config
import requests
from urllib.parse import unquote
from rest_framework.response import Response
#background noise reduction
# from pedalboard.io import AudioFile
# from pedalboard import *
# import noisereduce as nr
# from moviepy.editor import VideoFileClip
# from pydub import AudioSegment
# from moviepy.editor import VideoFileClip, AudioFileClip

def handle_uploaded_file(f):
    fs = FileSystemStorage()  # defaults to MEDIA_ROOT
    filename = fs.save(f.name, f)

    file_url = fs.url(filename)
    print(f"filename-------{file_url}----------{type(file_url)}")
    return file_url


def get_s3_signed_url_bykey(obj_key):
    print(f"keyyyyyyyyyyyyyyyyyy{obj_key}")
    my_config = Config(
        region_name='ap-south-1',
        signature_version='v4',

    )
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
        region_name=os.getenv('AWS_REGION'),
        config=my_config
    )

    #obj_key = a.split('/')[3]
    #print(f"keyyyyyyyyyyyy{obj_key}")

    response = s3_client.generate_presigned_url('get_object',
                                                Params={'Bucket': 'cuapp-files',
                                                        'Key': obj_key},
                                                ExpiresIn=3600)

    return response

def get_s3_signed_url(a):
    my_config = Config(
        region_name='ap-south-1',
        signature_version='v4',

    )
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
        region_name=os.getenv('AWS_REGION'),
        config=my_config
    )

    obj_key = a.split('/')[3]
    print(f"keyyyyyyyyyyyy{obj_key}")

    # response = s3_client.generate_presigned_url('get_object',
    #                                                 Params={'Bucket': 'cu-files',
    #                                                         'Key': obj_key},
    #                                                 ExpiresIn=3600)

    return obj_key


def handle_s3_uploaded_file(f_name,file_url):
    # Upload the file
    # using the IAM user testingsms
    my_config = Config(
        region_name='ap-south-1',
        signature_version='v4',

    )
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
        #region_name=os.getenv('AWS_REGION'),
        config=my_config
    )
    try:

        file_name_new = datetime.now().strftime("%Y-%m-%d-%H-%M-%S") + "-" + ''.join(
            random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for i in range(10)) + "_" + f_name
        #added
        file_url=unquote(file_url)
        #added
        response = s3_client.upload_file(file_url, "cuapp-files", file_name_new)
        print(f"innn handle functionnnnnnnnnnnn")
        os.remove(file_url)
        url = f"https://{os.getenv('AWS_S3_BUCKET')}.s3.ap-south-1.amazonaws.com/{file_name_new}"
        print("inside the s3 config",url)
        s_url=get_s3_signed_url(url)

        return s_url

    except Exception as e:
        # logging.error(e)
        print(f"exceptionnnnnnnnnnnnn{e}")
        return False

#background noise reduction
# def extract_audio(video, output):
#     # Load the video clip
#     video_clip = VideoFileClip(video)
#
#     # Extract the audio from the video
#     audio_clip = video_clip.audio
#
#     # Save the audio to a file
#     audio_clip.write_audiofile(output)  # or any other audio format
#
#     # Close the clips
#     video_clip.close()
#     audio_clip.close()



# def convert_mp3_to_wav(mp3_file, wav_file):
#     sound = AudioSegment.from_mp3(mp3_file)
#     sound.export(wav_file, format="wav")


# def reduce_noise(audio,new_wav_file):
#     sr = 44100
#     with AudioFile(audio).resampled_to(sr) as f:
#         audio = f.read(f.frames)
#
#     reduced_noise = nr.reduce_noise(y=audio, sr=sr, stationary=True, prop_decrease=0.75)
#
#     board = Pedalboard([
#         NoiseGate(threshold_db=180, ratio=1.5, release_ms=250),
#         Compressor(threshold_db=80, ratio=2.5),
#         LowShelfFilter(cutoff_frequency_hz=200, gain_db=30, q=1),
#         Gain(gain_db=80),
#         PeakFilter(cutoff_frequency_hz=100, gain_db=5)
#     ])
#
#     effected = board(reduced_noise, sr)
#
#     with AudioFile(new_wav_file, 'w', sr, effected.shape[0]) as f:
#         f.write(effected)


# def add_audio(video_path, audio_path):
#     video_clip = VideoFileClip(video_path)
#
#     audio_clip = AudioFileClip(audio_path)
#
#     video_clip = video_clip.set_audio(audio_clip)
#
#     video_clip.write_videofile('output_video.mp4', codec="libx264")
#
#     video_clip.close()
#     audio_clip.close()

#added this code of piece to insert doctor data into zoho account
def RefreshZohoSANDBOXAccessToken():
    deskurl = "https://accounts.zoho.in/oauth/v2/token?refresh_token="+os.getenv('ZOHO_SANDBOX_REFRESH_TOKEN')+"&grant_type=refresh_token&client_id="+os.getenv("ZOHODESK_CLIENT_ID")+"&client_secret="+os.getenv("ZOHODESK_CLIENT_SECRET")+"&redirect_uri=https://crmsandbox.zoho.in/crm/testing_modules/tab/Home/begin"
    r = requests.post(deskurl)
    a = r.json()

    return a['access_token']


def AddToZoho(doctor_data):
        d_data=[]
        d_name = doctor_data.name
        print(f'---------doctor_name{d_name}')
        doctor_email = doctor_data.email
        doctor_phone = doctor_data.phone
        datas = {
                "Account_Name": d_name,
                "Email_address": doctor_email,
                "Phone": doctor_phone,
                "Status":"You are a genius!!!!!!!!!!!",#doctor_data.approval,
        #         "Phone": "********",
        # "Website": "https://second_doctor.in/",
        # "Account Owner": "Oncofit Solutions"
            }
        d_data.append(datas)

        sending_data ={
            "data": d_data,
            "apply_feature_execution": [
            {
                "name": "layout_rules"
            }
        ],
         "duplicate_check_fields": [
                 "Email_address"
                     ],
        }    
            
        print(f'--------------------sending_data{sending_data}')    
        

        account_url = 'https://crmsandbox.zoho.in/crm/v6/Accounts/upsert'
        headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Layout': os.getenv("ZOHO_SANDBOX_ACCOUNT_LAYOUT_ID"),
                    'Authorization':'Bearer '+os.getenv("ZOHO_SANDBOX_ACCESS_TOKEN")
                }
        
        r = requests.post(account_url,headers=headers,json=sending_data)
        print(r)
        if r.status_code==401:
                        a=RefreshZohoSANDBOXAccessToken()
                        headers['Authorization']= 'Bearer ' + a
                        r = requests.post(account_url, headers=headers,json=sending_data)
                        print(r.status_code)
                        a = r.json()
  
                        return Response(a)
        return Response(r.json())   
    
def AddToZohoContacts(patient_data):
    p_data=[]
    p_name = patient_data.name
    print(f'---------doctor_name{p_name}')
    patient_email = patient_data.email
    patient_phone = patient_data.phone
    datas = {
            "Patient_Name": p_name,
            "Email": patient_email,
            "Phone": patient_phone,
            "Status":patient_data.approval,
            "Last_Name":p_name
        }
    p_data.append(datas)
    sending_data ={
        "data": p_data,
        "apply_feature_execution": [
        {
            "name": "layout_rules"
        }
    ],
    "duplicate_check_fields": [
                "Email_address"
                    ],
    }    
        
    print(f'--------------------sending_data{sending_data}')    


    account_url = 'https://crmsandbox.zoho.in/crm/v6/Contacts/upsert'
    headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Layout': os.getenv("ZOHO_SANDBOX_CONTACT_LAYOUT_ID"),
                'Authorization':'Bearer '+os.getenv("ZOHO_SANDBOX_ACCESS_TOKEN")
            }

    r = requests.post(account_url,headers=headers,json=sending_data)
    print(r)
    if r.status_code==401:
        a=RefreshZohoSANDBOXAccessToken()
        headers['Authorization']= 'Bearer ' + a
        r = requests.post(account_url, headers=headers,json=sending_data)
        print(r.status_code)
        a = r.json()

        return Response(a)
    return Response(r.json())

#-------------------for content removal by admin-----------------------------------
def ContentRemoval(removal_obj,removal_content):
    if removal_content=="patient_stories":
        removal_obj.status=10
        removal_obj.save()
    elif removal_content=="expert_feedback":
        removal_obj.status=10
        removal_obj.save()
    elif removal_content=="expert_blogs":
        removal_obj.BlogStatus=10
        removal_obj.save()
    elif removal_content=="doctor_reviews":
        removal_obj.ReviewStatus=10
        removal_obj.save()
    elif removal_content=="podcast":
        removal_obj.PodcastStatus=10
        removal_obj.save()
    else:
        return "Not removed"
    return "Content removed successfully"