# Generated by Django 4.2.5 on 2023-12-11 18:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0040_remove_cuuser_phoneotp_remove_cuuser_phoneotpgentime_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CuUserPhoneOTP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('UserEmail', models.CharField(blank=True, max_length=100, null=True)),
                ('PhoneOTP', models.IntegerField(blank=True, null=True)),
                ('PhoneOTPGenTime', models.DateTimeField(auto_now_add=True)),
                ('PhoneOTPVerified', models.BooleanField(default=False)),
            ],
        ),
    ]
