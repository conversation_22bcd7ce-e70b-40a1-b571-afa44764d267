from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib import colors
from django.conf import settings
from django.utils import timezone


def generate_invoice_pdf(response, invoice_data):
    print("invoice_data", invoice_data)
    c = canvas.Canvas(response, pagesize=letter)
    width, height = letter

    # Background color for header
    c.setFillColorRGB(0.96, 0.94, 0.99)
    c.rect(0, height - 100, width, 100, fill=1, stroke=0)

    # Logo (replace with your logo path)
    logo_path = settings.MEDIA_ROOT + "/logo.png"
    c.drawImage(logo_path, 40, height - 60, width=70, height=30)

    # Header text
    c.setFillColor(colors.HexColor("#9426b2"))
    c.setFont("Helvetica-Bold", 16)
    c.drawString(120, height - 50, "Invoice")

    # Get today's date for invoice date
    today = timezone.now().strftime("%d/%m/%Y")

    # Generate invoice number from appointment ID
    invoice_number = f"INVO-{invoice_data['appointment_id'][-5:]}"

    # Date and Invoice #
    c.setFont("Helvetica", 10)
    c.setFillColor(colors.HexColor("#9426b2"))
    c.drawRightString(width - 40, height - 40, today)
    c.drawRightString(width - 40, height - 55, invoice_number)

    # Horizontal divider
    c.setStrokeColor(colors.grey)
    c.setLineWidth(1)
    c.line(40, height - 110, width - 40, height - 110)

    # Billing and Shipping Information
    left_x = 50
    right_x = width / 2 + 10
    start_y = height - 130
    line_height = 20

    def draw_info_block(x, y, title, items):
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(colors.HexColor("#9426b2"))
        c.drawString(x, y, title)

        offset = 20
        for label, value in items:
            c.setFont("Helvetica-Bold", 10)
            c.setFillColor(colors.HexColor("#9426b2"))
            c.drawString(x, y - offset, label + ":")

            # Value color (#4A5051 - original)
            c.setFont("Helvetica", 10)
            c.setFillColor(colors.HexColor("#4A5051"))
            c.drawString(x + 80, y - offset, str(value))

            offset += 20
        return y - offset

    payment_date = invoice_data['payment_date']
    # Convert the string to a datetime object if it's a string
    if isinstance(payment_date, str):
        try:
            # Parse using timezone's datetime
            payment_date_obj = timezone.datetime.strptime(
                payment_date, '%Y-%m-%d %H:%M:%S %Z')
            formatted_payment_date = payment_date_obj.strftime(
                '%Y-%m-%d %H:%M')
        except:
            # Fallback if parsing fails
            formatted_payment_date = payment_date.split(
                ' ')[0] + ' ' + ':'.join(payment_date.split(' ')[1].split(':')[:2])
    else:
        # If it's already a datetime object
        formatted_payment_date = payment_date.strftime('%Y-%m-%d %H:%M')

    # Use the patient details from invoice_data
    billing_items = [
        ("Name", invoice_data['patient_name']),
        ("Patient ID", invoice_data['patient_id']),
        ("Payment Date", formatted_payment_date),
        ("Payment Status", invoice_data['payment_status'])
    ]

    # For shipping info, we'll keep the placeholder structure
    shipping_items = [
        ("Appointment ID", invoice_data['appointment_id']),
        ("Payment Intent", invoice_data['payment_intent'])
    ]

    draw_info_block(left_x, start_y, "Billing Information", billing_items)
    draw_info_block(right_x, start_y, "Payment Information", shipping_items)

    # Horizontal divider
    c.line(40, start_y - 130, width - 40, start_y - 130)

    # Table Header
    table_y = start_y - 180

    # Set header background color
    c.setFillColorRGB(0.96, 0.94, 0.99)
    c.rect(50, table_y, width - 100, 20, fill=1, stroke=0)

    # Set text color for headers
    c.setFillColor(colors.HexColor("#9426b2"))
    headers = ["", "Service", "Quantity", "Unit Price", "Total"]
    col_widths = [100, 180, 80, 80, 80]
    x = 50
    c.setFont("Helvetica-Bold", 10)
    for i, header in enumerate(headers):
        c.drawString(x + 5, table_y + 5, header)
        x += col_widths[i]

    # Format the amount with currency
    formatted_amount = f"{invoice_data['currency']} {invoice_data['amount']}"

    # Table Row with service details
    rows = [
        ("Online Medical Appointment", "", "1", formatted_amount, formatted_amount)
    ]

    c.setFont("Helvetica", 10)
    c.setFillColor(colors.HexColor("#4A5051"))
    row_height = 20
    y = table_y - row_height
    for row in rows:
        x = 50
        for i, cell in enumerate(row):
            c.drawString(x + 5, y + 5, str(cell))
            x += col_widths[i]
        y -= row_height

    # Fee Breakdown using the data from invoice_data
    c.setFont("Helvetica-Bold", 10)

    # Format all fee amounts with currency
    doctor_fee = f"{invoice_data['currency']} {invoice_data['doctor_fees']}"
    platform_charges = f"{invoice_data['currency']} {invoice_data['platform_charges']}"
    transaction_charges = f"{invoice_data['currency']} {invoice_data['transaction_charges']}"
    final_charge = f"{invoice_data['currency']} {invoice_data['final_charge']}"

    breakdowns = [
        ("Doctor Fee", doctor_fee),
        ("Platform Charges", platform_charges),
        ("Transaction Charges", transaction_charges),
    ]

    for label, amount in breakdowns:
        c.drawRightString(width - 150, y, label + ":")
        c.drawRightString(width - 80, y, amount)
        y -= 20

    # Total Due
    c.setFont("Helvetica-Bold", 12)
    c.setFillColor(colors.HexColor("#9426b2"))
    c.drawRightString(width - 150, y - 10, "Total:")
    c.setFont("Helvetica-Bold", 14)
    c.drawRightString(width - 80, y - 10, final_charge)

    c.showPage()
    c.save()
