# semantic_search.py
# import spacy
from cu_app.models.patient_models import ExpertiseCancertype
from cu_app.models.doctor_models import MedicalSpecialty


SPECIALTY_CONDITION_MAPPING = {
    "pulmonologist": [
        # Asthma
        "asthma", "asthma attack", "wheezing", "shortness of breath", "breathing problems",
        "tight chest", "bronchial asthma",

        # COPD
        "copd", "chronic bronchitis", "emphysema", "smoker's lung", "lung damage",
        "difficulty breathing", "chronic cough",

        # Pneumonia
        "pneumonia", "lung infection", "chest infection", "cough with fever",
        "fluid in lungs",

        # Lung Cancer
        "lung cancer", "coughing blood", "persistent cough", "lung tumor",
        "chest x-ray abnormality"
    ],
    "gastroenterologist": [
        # St<PERSON>ch Pain
        "stomach pain", "abdominal pain", "tummy ache", "belly pain", "stomach cramps",
        "upper abdominal discomfort",

        # Digestive Issues
        "diarrhea", "loose stools", "constipation", "irregular bowel movements",
        "bloating", "gas", "indigestion", "heartburn", "acid reflux", "gerd",

        # Live<PERSON>/Gallbladder
        "yellow skin", "jaundice", "yellow eyes", "dark urine", "light stools",
        "liver pain", "gallstones", "fatty liver"
    ],
    "urologist": [
        # Urinary
        "urinary infection", "uti", "burning pee", "frequent urination",
        "blood in urine", "cloudy urine", "urgency to pee",

        # Kidney
        "kidney stones", "renal calculi", "flank pain", "back pain", "painful urination",
        "kidney infection",

        # Prostate
        "prostate issues", "enlarged prostate", "bph", "weak urine stream",
        "frequent nighttime urination",

        # Bladder
        "bladder problem", "overactive bladder", "urinary incontinence",
        "leaking urine", "interstitial cystitis"
    ],
    "hepatologist": [
        # Liver Disease
        "liver disease", "hepatitis", "hepatitis b", "hepatitis c", "cirrhosis",
        "liver damage", "liver failure", "liver pain", "right upper quadrant pain",

        # Jaundice
        "jaundice", "yellow skin", "yellow eyes", "itchy skin",

        # Metabolic
        "fatty liver", "nafld", "nash", "elevated liver enzymes",
        "abnormal liver function tests"
    ],
    # Add other specialties following the same pattern
    "cardiologist": [
        "chest pain", "heart pain", "palpitations", "irregular heartbeat",
        "high blood pressure", "hypertension", "shortness of breath",
        "swollen ankles", "heart failure", "angina", "heart attack"
    ]
}


class SemanticSearch:
    def __init__(self):
        # self.nlp = spacy.load("en_core_sci_sm")
        self.specialty_mapping = self._build_specialty_mapping()

    def _build_specialty_mapping(self):
        """Combine database specialties with our manual mapping"""
        specialties = ExpertiseCancertype.objects.values_list(
            "name", flat=True).distinct()
        mapping = {}

        for specialty in specialties:
            lower_spec = specialty.lower()
            # Combine manual mappings with NLP-extracted terms
            manual_terms = SPECIALTY_CONDITION_MAPPING.get(lower_spec, [])
            nlp_terms = self._extract_terms_from_specialty(specialty)
            mapping[lower_spec] = list(set(manual_terms + nlp_terms))
        return mapping

    # def _build_specialty_mapping(self):
    #     """Fetch specialty-condition mapping from the database dynamically"""
    #     mapping = {}

    #     # Get all specialties with their terms
    #     for specialty in MedicalSpecialty.objects.all():
    #         lower_spec = specialty.name.lower()  # Convert specialty name to lowercase

    #         # Get terms from the database (they are stored as JSON)
    #         db_terms = specialty.terms  # This is already a list from JSONField
    #         print("db_terms", db_terms)

    #         # Extract additional NLP terms if needed
    #         nlp_terms = self._extract_terms_from_specialty(specialty.name)
    #         print("nlp_terms", nlp_terms)

    #         # Combine terms and remove duplicates
    #         mapping[lower_spec] = list(set(db_terms + nlp_terms))

    #     print("mapping", mapping)
    #     return mapping

    def _extract_terms_from_specialty(self, specialty):
        """Extract additional terms from specialty name using NLP"""
        doc = self.nlp(specialty)
        print("terms for speciality", [ent.text.lower() for ent in doc.ents])
        return [ent.text.lower() for ent in doc.ents]

    def _lemmatize_terms(self, terms):
        """Convert list of terms into base form (lemmas)"""
        lemmatized_terms = set()
        for term in terms:
            doc = self.nlp(term)
            lemma_term = " ".join([token.lemma_ for token in doc])
            lemmatized_terms.add(lemma_term.lower())
        return list(lemmatized_terms)

    def extract_medical_terms(self, query):
        """Enhanced term extraction with symptom detection"""
        doc = self.nlp(query)
        terms = [ent.text.lower() for ent in doc.ents]

        # Add symptom detection
        symptoms = [chunk.text.lower() for chunk in doc.noun_chunks
                    if any(tok in ['pain', 'ache', 'discomfort'] for tok in chunk.text.lower().split())]

        # Apply lemmatization
        lemmatized_terms = {token.lemma_.lower()
                            for token in doc if token.is_alpha}

        print("Lemmatized Terms:", lemmatized_terms)
        print("Symptoms:", symptoms)
        print("Extracted Medical Terms:", terms)

        return list(set(terms) | set(symptoms) | lemmatized_terms)

    def find_matching_specialties(self, query):
        """Return only specialties that contain the exact query terms"""
        try:
            query_terms = self.extract_medical_terms(
                query)  # Extract relevant medical terms
            query_terms = self._lemmatize_terms(query_terms)  # Normalize terms

            if not query_terms:
                print("⚠️ No medical terms found in query")
                return []

            matched_specialties = set()

            for specialty, terms in self.specialty_mapping.items():
                # Check if any query term appears in the list of terms for the specialty
                if any(q_term in terms for q_term in query_terms):
                    matched_specialties.add(specialty)

            print(
                f"✅ Matched Specialties: {matched_specialties}" if matched_specialties else "❌ No matches found")
            return list(matched_specialties)

        except Exception as e:
            print(f"🔥 ERROR in find_matching_specialties: {e}")
            return []
