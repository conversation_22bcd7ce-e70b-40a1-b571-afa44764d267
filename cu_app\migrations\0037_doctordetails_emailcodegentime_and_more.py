# Generated by Django 4.2.5 on 2023-12-02 12:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0036_alter_cuuser_date_of_birth'),
    ]

    operations = [
        migrations.AddField(
            model_name='doctordetails',
            name='EmailCodeGentime',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='doctordetails',
            name='EmailVerified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='doctordetails',
            name='EmailVerifyCode',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='patientdetails',
            name='EmailCodeGentime',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='patientdetails',
            name='EmailVerified',
            field=models.<PERSON><PERSON>anField(default=False),
        ),
        migrations.AddField(
            model_name='patientdetails',
            name='EmailVerifyCode',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
    ]
