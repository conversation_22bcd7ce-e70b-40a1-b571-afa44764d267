# Generated by Django 4.2.5 on 2023-11-17 15:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0023_doctordetails_profilephoto_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientQueries',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('PQuery', models.TextField(blank=True, null=True)),
                ('ReplyTo', models.IntegerField(blank=True, null=True)),
                ('ApptId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
            ],
        ),
    ]
