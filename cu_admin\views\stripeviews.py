from django.shortcuts import render
from rest_framework import generics, status
from django.middleware.csrf import get_token
from django.http import JsonResponse
from ..serializers import *
from datetime import *
from rest_framework.response import Response
import json
from django.contrib.auth.models import Group, Permission
from django.views import View
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import authenticate, login, logout, get_user_model
from cu_app.models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

import re
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from cu_app.cu_library import *

from rest_framework.parsers import JSONParser
#from pytz import timezone
from rest_framework.exceptions import APIException
import random
from django.db.models import Q
from rest_framework import permissions
from dotenv import load_dotenv
import stripe
import requests
from django.utils.dateparse import parse_datetime
from django.utils import timezone

ENV_ROOT = os.path.join(settings.BASE_DIR, '.env')
load_dotenv(ENV_ROOT)

def CreatePrice(id,a):
    prod_price = a.Price.create(
        unit_amount=1200,
        currency="usd",
        recurring={"interval": "month"},
        product=id,
    )
    print(f"price id: {prod_price.id}----------{prod_price}")
    return prod_price

def CreatePrice1(id,a):
    prod_price = a.Price.create(
        unit_amount=100,
        currency="usd",
        product=id,
    )
    print(f"price id: {prod_price.id}----------{prod_price}")
    return prod_price

@method_decorator(csrf_exempt,name="dispatch")
class CreateProdView(View):

    def post(self, request, *args, **kwargs):
        u_data = json.loads(request.body.decode("utf-8"))
        print(f"dataaa----------{request.body}----------{u_data}")
        #stripe.api_key =settings.STRIPE_API_KEY
        stripe.api_key = os.getenv("STRIPE_API_KEY")

        prod_name = stripe.Product.create(
            name=u_data['p_name'],
            description=u_data['p_desc'],
        )
        print(f"product id: {prod_name.id}--------------{prod_name}----{type(prod_name)}---{prod_name is not None}")
        if prod_name is not None:
            return JsonResponse({"message":"product created"})
        else:
            return JsonResponse({"message": "product not created"})

@method_decorator(csrf_exempt,name="dispatch")
class SetPriceView(View):
    def post(self,r):
        #stripe.api_key = settings.STRIPE_API_KEY
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        pid="prod_PBZyTzZWDISEUb"
        a=CreatePrice1(pid,stripe)
        print(f"price id: {a.id}--------------{a}----{type(a)}---{a is not None}")
        if a is not None:
            return JsonResponse({"message": "price set"})
        else:
            return JsonResponse({"message": "price not set"})



@method_decorator(csrf_exempt,name="dispatch")
class CreatePLinkView(View):
    def post(self,r):

        #stripe.api_key = settings.STRIPE_API_KEY
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        priceid="price_1ONDUmAO8UGO7lmnG0QRmwJO"

        a=stripe.PaymentLink.create(line_items=[{"price": priceid, "quantity": 1}], after_completion={"type": "redirect", "redirect": {"url": "https://example.com?sid={{CHECKOUT_SESSION_ID}}"}})
        print(f"payment id:--------------{a}----{type(a)}---{a is not None}")
        if a is not None:
            return JsonResponse({"message": "paymt link created"})
        else:
            return JsonResponse({"message": "paymt link not created"})

    def get(self,r):
        #stripe.api_key = settings.STRIPE_API_KEY
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        a=stripe.checkout.Session.retrieve(
            "cs_test_a1JDSJvntMHSn9PBJB3gYC4LgZBw2lS5cpq5wgS4UMtN6W39d4VfJE9nya",
        )
        print(f"session details:--------------{a}----{type(a)}---{a is not None}")
        if a is not None:
            return JsonResponse({"message": "session details"})
        else:
            return JsonResponse({"message": "no session details"})

class PaymentIntentsView(View):
    def get(self,r):
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        a=stripe.PaymentIntent.retrieve('pi_3OkMfbAO8UGO7lmn0mxDmfuC')
        b=stripe.checkout.Session.retrieve(
  "cs_test_a13PTQemvCTerveMifzAOUyDb2uAFIoi0t3bT4DWW5a5sXVdivVqO0AasQ",
)

        print(f"payment intent details:--------------{b.created}")
        # for x in a['data']:
        #
        #     if x['customer'] is not None:
        #         cus_details=stripe.Customer.retrieve(x['customer'])
        #         print(f"customer-------{cus_details}")
        p_date = timezone.make_aware(datetime.fromtimestamp(b.created),
                                                    timezone.get_current_timezone())
        #p_date=datetime.fromtimestamp(b.created)
        print(f"payment intent details:--------------{p_date}")
        return JsonResponse({"data":a})

class GetBalanceView(View):
    def get(selfself,r):
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        a=stripe.Balance.retrieve()
        print(f"balance details:--------------{a}----{type(a)}---{a is not None}")
        return JsonResponse({"data":a})








