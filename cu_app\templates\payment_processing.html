{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Processing | Health Unwired</title>
    <!-- <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon"> -->
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f7f9fc;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .container {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 90%;
            width: 500px;
        }
        
        h1 {
            color: #2d5986;
            margin-bottom: 20px;
        }
        
        p {
            margin-bottom: 30px;
            line-height: 1.6;
            color: #666;
        }
        
        .loader {
            border: 6px solid #f3f3f3;
            border-radius: 50%;
            border-top: 6px solid #2d5986;
            width: 50px;
            height: 50px;
            margin: 20px auto;
            animation: spin 2s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .logo {
            max-width: 180px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        {% if logo_url %}
        <img src="/media/logo.png" alt="Health Unwired" class="logo">
        {% endif %}
        
        <h1>Payment Successful!</h1>
        <p>Your appointment has been booked successfully. We're redirecting you to the confirmation page.</p>
        <div class="loader"></div>
        <p id="redirect-message">You will be redirected in a few seconds...</p>
    </div>

    <script>
        // This script handles redirection for Airwallex payments
        document.addEventListener('DOMContentLoaded', function() {
            // Extract payment ID from URL if available
            const urlParams = new URLSearchParams(window.location.search);
            const paymentId = urlParams.get('payment_intent_id') || 
                              urlParams.get('id') || 
                              urlParams.get('payment_id');
            
            // Function to check payment status and redirect
            function redirectToSuccessPage() {
                // Build the destination URL with any available payment ID
                let destinationUrl = "https://healthunwired.com/paymentsuccess";
                if (paymentId) {
                    destinationUrl += `?payment_id=${paymentId}`;
                }
                
                // If we're on airwallex.com or a subdomain
                if (window.location.hostname.includes('airwallex') || 
                    window.location.hostname.includes('pay-demo')) {
                    console.log("Detected Airwallex page, redirecting to success page...");
                }
                
                // Update redirect message
                document.getElementById('redirect-message').textContent = 'Redirecting to confirmation page...';
                
                // Redirect after a short delay
                setTimeout(function() {
                    window.location.href = destinationUrl;
                }, 2000); // 2 seconds delay for user experience
            }
            
            // Initiate redirect
            redirectToSuccessPage();
        });
    </script>
</body>
</html>