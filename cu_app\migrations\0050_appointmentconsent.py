# Generated by Django 4.2.5 on 2023-12-29 10:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0049_doctordetails_ndaconsent_patientdetails_ndaconsent_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppointmentConsent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('PatientConsent', models.IntegerField(default=0)),
                ('ExpertConsent', models.IntegerField(default=0)),
                ('AppId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
            ],
        ),
    ]
