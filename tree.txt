Folder PATH listing
Volume serial number is 2A00-8271
C:.
|   .env
|   .env.db.production
|   .env.localdb
|   .gitignore
|   application_default_credentials.json
|   credentials.json
|   Dockerfile
|   Jenkinsfile
|   manage.py
|   README.md
|   requirements.txt
|   token.json
|   tree.txt
|   
+---.vscode
|       PythonImportHelper-v2-Completion.json
|       
+---cu_admin
|   |   admin.py
|   |   apps.py
|   |   models.py
|   |   serializers.py
|   |   tests.py
|   |   urls.py
|   |   user_notifications.py
|   |   __init__.py
|   |   
|   +---migrations
|   |   |   0001_initial.py
|   |   |   0002_timezonetestmodel_user_timezone.py
|   |   |   __init__.py
|   |   |   
|   |   \---__pycache__
|   |           0001_initial.cpython-311.pyc
|   |           0002_timezonetestmodel_user_timezone.cpython-311.pyc
|   |           __init__.cpython-311.pyc
|   |           
|   +---views
|   |   |   stripeviews.py
|   |   |   views1.py
|   |   |   __init__.py
|   |   |   
|   |   \---__pycache__
|   |           stripeviews.cpython-311.pyc
|   |           views1.cpython-311.pyc
|   |           __init__.cpython-311.pyc
|   |           
|   \---__pycache__
|           admin.cpython-311.pyc
|           apps.cpython-311.pyc
|           models.cpython-311.pyc
|           serializers.cpython-311.pyc
|           urls.cpython-311.pyc
|           user_notifications.cpython-311.pyc
|           __init__.cpython-311.pyc
|           
+---cu_app
|   |   admin.py
|   |   apps.py
|   |   backends.py
|   |   cu_library.py
|   |   firebase_app_management.py
|   |   forms.py
|   |   middleware.py
|   |   serializers.py
|   |   tests.py
|   |   urls.py
|   |   __init__.py
|   |   
|   +---migrations
|   |   |   0001_initial.py
|   |   |   0002_cu_permissions.py
|   |   |   0003_expertisecancertype_cuuser_expertise.py
|   |   |   0004_alter_cuuser_expertise.py
|   |   |   0005_patient_medical_records.py
|   |   |   0006_alter_expertisecancertype_name.py
|   |   |   0007_alter_expertisecancertype_name.py
|   |   |   0008_alter_expertisecancertype_name.py
|   |   |   0009_schedulerslots_appointments.py
|   |   |   0010_appointments_location_appointments_summary_and_more.py
|   |   |   0011_alter_schedulerslots_timezone.py
|   |   |   0012_appointments_description.py
|   |   |   0013_alter_appointments_slot_id.py
|   |   |   0014_testjson.py
|   |   |   0015_doctordetails.py
|   |   |   0016_doctordetails_experience.py
|   |   |   0017_prescription_oralmedication_ivimmedication.py
|   |   |   0018_alter_cuuser_phone.py
|   |   |   0019_cuuser_age_cuuser_cancer_type_cuuser_sex.py
|   |   |   0020_appointments_dateoffixingapp_and_more.py
|   |   |   0021_patientdetails.py
|   |   |   0022_alter_doctordetails_timezone.py
|   |   |   0023_doctordetails_profilephoto_and_more.py
|   |   |   0024_patientqueries.py
|   |   |   0025_patientqueries_querytime.py
|   |   |   0026_alter_patientqueries_apptid.py
|   |   |   0027_cuuser_user_login_time.py
|   |   |   0028_remove_cuuser_user_login_time.py
|   |   |   0029_meetingsession.py
|   |   |   0030_alter_meetingsession_issuccess_and_more.py
|   |   |   0031_meetingsession_patientjointime_and_more.py
|   |   |   0032_meetingsession_patientjoinapproval.py
|   |   |   0033_meetingsession_patientrequest_and_more.py
|   |   |   0034_cuuser_dateofregistration.py
|   |   |   0035_alter_cuuser_approval.py
|   |   |   0036_alter_cuuser_date_of_birth.py
|   |   |   0037_doctordetails_emailcodegentime_and_more.py
|   |   |   0038_patientstories_expertblogs.py
|   |   |   0039_cuuser_phoneotp_cuuser_phoneotpgentime_and_more.py
|   |   |   0040_remove_cuuser_phoneotp_remove_cuuser_phoneotpgentime_and_more.py
|   |   |   0041_cuuserphoneotp.py
|   |   |   0042_expertblogs_blogstatus.py
|   |   |   0043_cuuser_timezone_alter_expertblogs_blogstatus.py
|   |   |   0044_remove_doctordetails_timezone_and_more.py
|   |   |   0045_doctordetails_intvideourl_and_more.py
|   |   |   0046_alter_doctordetails_intvideourl.py
|   |   |   0047_alter_doctordetails_intvideourl.py
|   |   |   0048_doctordetails_consultationfees.py
|   |   |   0049_doctordetails_ndaconsent_patientdetails_ndaconsent_and_more.py
|   |   |   0050_appointmentconsent.py
|   |   |   0051_alter_appointmentconsent_appid.py
|   |   |   0052_alter_appointmentconsent_appid.py
|   |   |   0053_statusreason_reasoncategory_statusreason_reasontype.py
|   |   |   0054_expertfeedback.py
|   |   |   0055_expertfeedback_status.py
|   |   |   0056_patientstories_status.py
|   |   |   0057_patientstories_currenttime_and_more.py
|   |   |   0058_alter_expertfeedback_status.py
|   |   |   0059_pricing.py
|   |   |   0060_alter_cuuser_cancer_type.py
|   |   |   0061_doctordetails_signature.py
|   |   |   0062_irprescription.py
|   |   |   0063_irprescription_doctorsignature.py
|   |   |   0064_cuuser_pwcodegentime_cuuser_pwverifycode.py
|   |   |   0065_alter_patientdetails_emailcodegentime.py
|   |   |   0066_alter_patientdetails_emailcodegentime.py
|   |   |   0067_alter_patientdetails_emailcodegentime.py
|   |   |   0068_alter_patientdetails_emailcodegentime.py
|   |   |   0069_alter_cuuser_pwcodegentime_and_more.py
|   |   |   0070_alter_cuuser_pwcodegentime.py
|   |   |   0071_patientdetails_signature.py
|   |   |   0072_doctordetails_reason_doctordetails_reasondate.py
|   |   |   0073_alter_patientdetails_profilephoto.py
|   |   |   0074_alter_doctordetails_profilephoto_and_more.py
|   |   |   0075_s3objectskeys.py
|   |   |   0076_alter_doctordetails_intvideourl.py
|   |   |   0077_pushnotifications.py
|   |   |   0078_alter_irprescription_doctorsignature_and_more.py
|   |   |   0079_pushnotifications_body_pushnotifications_link_and_more.py
|   |   |   0080_pushnotifications_notificationtime.py
|   |   |   0081_remove_pushnotifications_notificationid.py
|   |   |   0082_doctordetails_introvideostatus.py
|   |   |   0083_alter_doctordetails_introvideostatus.py
|   |   |   0084_alter_doctordetails_introvideostatus.py
|   |   |   0085_appointmentmgmt.py
|   |   |   0086_alter_cuuser_timezone.py
|   |   |   0087_patientpayments.py
|   |   |   0088_patientpayments_checkoutsessionid_and_more.py
|   |   |   0089_appointments_invoice_alter_appointments_description_and_more.py
|   |   |   0090_doctordetails_experiencesummary.py
|   |   |   0091_doctordetails_researchpapers.py
|   |   |   0092_doctorreviews.py
|   |   |   0093_patientdetails_sociallogin.py
|   |   |   0094_doctorconsent.py
|   |   |   0095_cuuser_city.py
|   |   |   0096_cuuser_country.py
|   |   |   0097_appointmentconsent_patientconsent_doctorform_and_more.py
|   |   |   0098_alter_patient_medical_records_report_file.py
|   |   |   0099_doctordetails_expertrole_and_more.py
|   |   |   0100_remove_doctordetails_expertrole.py
|   |   |   0101_alter_expertblogs_blogimages.py
|   |   |   0102_alter_doctordetails_otherachievements.py
|   |   |   0103_doctorreviews_reviewrating.py
|   |   |   0104_schedulerslots_status.py
|   |   |   0105_refundedpayments_alter_schedulerslots_status.py
|   |   |   0106_refundedpayments_refundobject.py
|   |   |   0107_alter_refundedpayments_refundobject.py
|   |   |   0108_appointmentmgmt_appointmentreason_and_more.py
|   |   |   0109_alter_doctorreviews_patientemail.py
|   |   |   0110_alter_appointments_status.py
|   |   |   0111_notificationtype.py
|   |   |   0112_alter_appointments_status.py
|   |   |   0113_alter_expertblogs_blogimages.py
|   |   |   0114_alter_expertblogs_blogimages.py
|   |   |   0115_alter_expertblogs_blogimages.py
|   |   |   0116_alter_refundedpayments_refundobject.py
|   |   |   0117_alter_expertblogs_blogimages_and_more.py
|   |   |   0118_alter_expertblogs_blogimages.py
|   |   |   0119_alter_schedulerslots_options.py
|   |   |   0120_alter_schedulerslots_options_and_more.py
|   |   |   0121_admindetails.py
|   |   |   0122_doctorconsent_status.py
|   |   |   0123_alter_doctorconsent_status.py
|   |   |   0124_podcast.py
|   |   |   0125_admindetails_phoneverified.py
|   |   |   0126_alter_patient_medical_records_generation_date.py
|   |   |   0127_reporttype.py
|   |   |   0128_doctorreviews_reviewstatus.py
|   |   |   0129_alter_doctordetails_intvideourl.py
|   |   |   0129_meetingsession_doctorallowpatient.py
|   |   |   0130_merge_20240601_1617.py
|   |   |   0131_alter_meetingsession_doctorallowpatient.py
|   |   |   0132_alter_meetingsession_meetingstatus.py
|   |   |   0133_doctordetails_dateofactivation.py
|   |   |   0134_alter_patient_medical_records_report_file.py
|   |   |   0135_doctorconsent_dateofconsentform.py
|   |   |   0136_admincontentmanagement.py
|   |   |   0137_refundedpayments_appid.py
|   |   |   0138_refundedpayments_refundcurrency_and_more.py
|   |   |   0139_alter_refundedpayments_refunddate.py
|   |   |   0140_podcast_podcastdate.py
|   |   |   0141_alter_doctorreviews_reviewstatus_and_more.py
|   |   |   0142_podcast_thumbnailimage.py
|   |   |   0143_expertwallet_expertwallettransactions.py
|   |   |   0144_alter_expertwallet_id.py
|   |   |   0145_faqs_expertblogs_blogviews_podcast_podcastcategory_and_more.py
|   |   |   0146_expertblogs_blogcategory_expertblogs_blogsubbody_and_more.py
|   |   |   0147_blogcategory_podcastcategory.py
|   |   |   0148_expertblogs_blogbannerimage_and_more.py
|   |   |   0149_blogsection_podcastsection_and_more.py
|   |   |   0150_expertwallettransactions_cleareddate.py
|   |   |   0151_blogcategory_description_podcastcategory_description.py
|   |   |   0152_podcast_podcasttimestamp_podcast_podcasttranscript.py
|   |   |   0153_cuuser_country_code_cuuser_prefix_and_more.py
|   |   |   0154_rename_podcasturl_podcast_platforms.py
|   |   |   0155_alter_expertwallettransactions_transactiontype.py
|   |   |   0156_alter_podcast_thumbnailimage.py
|   |   |   0157_doctordetails_commissionpercentage_and_more.py
|   |   |   0158_expertrank.py
|   |   |   0159_updatebanner.py
|   |   |   0160_alter_updatebanner_updatetime.py
|   |   |   0161_podcast_helpfullinks.py
|   |   |   0162_alter_cuuser_name_alter_doctordetails_address_and_more.py
|   |   |   0163_randomexpert.py
|   |   |   0164_commontopics.py
|   |   |   0165_alter_expertfeedback_feedback.py
|   |   |   0166_expertfeedback_rating.py
|   |   |   0167_contentselection.py
|   |   |   0168_contentselection_rank.py
|   |   |   0169_rename_categoryreferenceid_contentselection_c_id_and_more.py
|   |   |   0170_alter_contentselection_rank.py
|   |   |   0171_videoslibrary.py
|   |   |   0172_videoslibrary_video_file.py
|   |   |   0173_remove_videoslibrary_video_file_and_more.py
|   |   |   0174_remove_videoslibrary_videourl_and_more.py
|   |   |   0175_alter_videoslibrary_videotitle_and_more.py
|   |   |   0176_alter_videoslibrary_videotitle.py
|   |   |   0177_alter_videoslibrary_videotitle.py
|   |   |   0178_alter_videoslibrary_videotitle_and_more.py
|   |   |   0179_alter_videoslibrary_videotitle_and_more.py
|   |   |   0180_remove_videoslibrary_videotitle_and_more.py
|   |   |   0181_remove_videoslibrary_video_url.py
|   |   |   0182_alter_videoslibrary_video_file_and_more.py
|   |   |   0183_expertbankdetails.py
|   |   |   0184_alter_expertbankdetails_account_holder.py
|   |   |   0185_videoslibrary_isurl.py
|   |   |   0186_videoslibrary_thumbnail_image.py
|   |   |   0187_alter_videoslibrary_video_file.py
|   |   |   0188_usersubscription.py
|   |   |   0189_remove_usersubscription_interests.py
|   |   |   0190_alter_videoslibrary_thumbnail_image.py
|   |   |   0191_alter_usersubscription_is_subscribed.py
|   |   |   0192_remove_expertbankdetails_account_number_and_more.py
|   |   |   0193_usersubscription_unsubscribe_reason.py
|   |   |   0194_usersubscription_phone.py
|   |   |   0195_usersubscription_c_code.py
|   |   |   0196_alter_usersubscription_c_code.py
|   |   |   0197_medicalspecialty.py
|   |   |   0198_cuuserphoneotp_tempcountrycode_and_more.py
|   |   |   0199_alter_cuuserphoneotp_phoneotp.py
|   |   |   0200_cuuserphoneotp_tempuserdata.py
|   |   |   0201_remove_cuuserphoneotp_tempcountrycode_and_more.py
|   |   |   0202_cuuserphoneotp_tempcountrycode_and_more.py
|   |   |   0203_cuuserphoneotp_otpexpired.py
|   |   |   0204_remove_medicalspecialty_terms_cuuser_embedding_and_more.py
|   |   |   0205_cuuserphoneotp_hashedpassword.py
|   |   |   0206_remove_cuuser_embedding_doctordetails_embedding.py
|   |   |   0207_remove_doctordetails_embedding_embedding.py
|   |   |   0208_patientpayment_testmodel_delete_patientpayments.py
|   |   |   0209_delete_testmodel.py
|   |   |   0210_alter_expertwallettransactions_cleareddate.py
|   |   |   0211_patientpayment_raw_status_and_more.py
|   |   |   0212_refundedpayments_refundid.py
|   |   |   __init__.py
|   |   |   
|   |   \---__pycache__
|   |           0001_initial.cpython-311.pyc
|   |           0002_cu_permissions.cpython-311.pyc
|   |           0003_expertisecancertype_cuuser_expertise.cpython-311.pyc
|   |           0004_alter_cuuser_expertise.cpython-311.pyc
|   |           0005_patient_medical_records.cpython-311.pyc
|   |           0006_alter_expertisecancertype_name.cpython-311.pyc
|   |           0007_alter_expertisecancertype_name.cpython-311.pyc
|   |           0008_alter_expertisecancertype_name.cpython-311.pyc
|   |           0009_schedulerslots_appointments.cpython-311.pyc
|   |           0010_appointments_location_appointments_summary_and_more.cpython-311.pyc
|   |           0011_alter_schedulerslots_timezone.cpython-311.pyc
|   |           0012_appointments_description.cpython-311.pyc
|   |           0013_alter_appointments_slot_id.cpython-311.pyc
|   |           0014_testjson.cpython-311.pyc
|   |           0015_doctordetails.cpython-311.pyc
|   |           0016_doctordetails_experience.cpython-311.pyc
|   |           0017_prescription_oralmedication_ivimmedication.cpython-311.pyc
|   |           0018_alter_cuuser_phone.cpython-311.pyc
|   |           0019_cuuser_age_cuuser_cancer_type_cuuser_sex.cpython-311.pyc
|   |           0020_appointments_dateoffixingapp_and_more.cpython-311.pyc
|   |           0021_patientdetails.cpython-311.pyc
|   |           0022_alter_doctordetails_timezone.cpython-311.pyc
|   |           0023_doctordetails_profilephoto_and_more.cpython-311.pyc
|   |           0024_patientqueries.cpython-311.pyc
|   |           0025_patientqueries_querytime.cpython-311.pyc
|   |           0026_alter_patientqueries_apptid.cpython-311.pyc
|   |           0027_cuuser_user_login_time.cpython-311.pyc
|   |           0028_remove_cuuser_user_login_time.cpython-311.pyc
|   |           0029_meetingsession.cpython-311.pyc
|   |           0030_alter_meetingsession_issuccess_and_more.cpython-311.pyc
|   |           0031_meetingsession_patientjointime_and_more.cpython-311.pyc
|   |           0032_meetingsession_patientjoinapproval.cpython-311.pyc
|   |           0033_meetingsession_patientrequest_and_more.cpython-311.pyc
|   |           0034_cuuser_dateofregistration.cpython-311.pyc
|   |           0035_alter_cuuser_approval.cpython-311.pyc
|   |           0036_alter_cuuser_date_of_birth.cpython-311.pyc
|   |           0037_doctordetails_emailcodegentime_and_more.cpython-311.pyc
|   |           0038_patientstories_expertblogs.cpython-311.pyc
|   |           0039_cuuser_phoneotp_cuuser_phoneotpgentime_and_more.cpython-311.pyc
|   |           0040_remove_cuuser_phoneotp_remove_cuuser_phoneotpgentime_and_more.cpython-311.pyc
|   |           0041_cuuserphoneotp.cpython-311.pyc
|   |           0042_expertblogs_blogstatus.cpython-311.pyc
|   |           0043_cuuser_timezone_alter_expertblogs_blogstatus.cpython-311.pyc
|   |           0044_remove_doctordetails_timezone_and_more.cpython-311.pyc
|   |           0045_doctordetails_intvideourl_and_more.cpython-311.pyc
|   |           0046_alter_doctordetails_intvideourl.cpython-311.pyc
|   |           0047_alter_doctordetails_intvideourl.cpython-311.pyc
|   |           0048_doctordetails_consultationfees.cpython-311.pyc
|   |           0049_doctordetails_ndaconsent_patientdetails_ndaconsent_and_more.cpython-311.pyc
|   |           0050_appointmentconsent.cpython-311.pyc
|   |           0051_alter_appointmentconsent_appid.cpython-311.pyc
|   |           0052_alter_appointmentconsent_appid.cpython-311.pyc
|   |           0053_statusreason_reasoncategory_statusreason_reasontype.cpython-311.pyc
|   |           0054_expertfeedback.cpython-311.pyc
|   |           0055_expertfeedback_status.cpython-311.pyc
|   |           0056_patientstories_status.cpython-311.pyc
|   |           0057_patientstories_currenttime_and_more.cpython-311.pyc
|   |           0058_alter_expertfeedback_status.cpython-311.pyc
|   |           0059_pricing.cpython-311.pyc
|   |           0060_alter_cuuser_cancer_type.cpython-311.pyc
|   |           0061_doctordetails_signature.cpython-311.pyc
|   |           0062_irprescription.cpython-311.pyc
|   |           0063_irprescription_doctorsignature.cpython-311.pyc
|   |           0064_cuuser_pwcodegentime_cuuser_pwverifycode.cpython-311.pyc
|   |           0065_alter_patientdetails_emailcodegentime.cpython-311.pyc
|   |           0066_alter_patientdetails_emailcodegentime.cpython-311.pyc
|   |           0067_alter_patientdetails_emailcodegentime.cpython-311.pyc
|   |           0068_alter_patientdetails_emailcodegentime.cpython-311.pyc
|   |           0069_alter_cuuser_pwcodegentime_and_more.cpython-311.pyc
|   |           0070_alter_cuuser_pwcodegentime.cpython-311.pyc
|   |           0071_patientdetails_signature.cpython-311.pyc
|   |           0072_doctordetails_reason_doctordetails_reasondate.cpython-311.pyc
|   |           0073_alter_patientdetails_profilephoto.cpython-311.pyc
|   |           0074_alter_doctordetails_profilephoto_and_more.cpython-311.pyc
|   |           0075_s3objectskeys.cpython-311.pyc
|   |           0076_alter_doctordetails_intvideourl.cpython-311.pyc
|   |           0077_pushnotifications.cpython-311.pyc
|   |           0078_alter_irprescription_doctorsignature_and_more.cpython-311.pyc
|   |           0079_pushnotifications_body_pushnotifications_link_and_more.cpython-311.pyc
|   |           0080_pushnotifications_notificationtime.cpython-311.pyc
|   |           0081_remove_pushnotifications_notificationid.cpython-311.pyc
|   |           0082_doctordetails_introvideostatus.cpython-311.pyc
|   |           0083_alter_doctordetails_introvideostatus.cpython-311.pyc
|   |           0084_alter_doctordetails_introvideostatus.cpython-311.pyc
|   |           0085_appointmentmgmt.cpython-311.pyc
|   |           0086_alter_cuuser_timezone.cpython-311.pyc
|   |           0087_patientpayments.cpython-311.pyc
|   |           0088_patientpayments_checkoutsessionid_and_more.cpython-311.pyc
|   |           0089_appointments_invoice_alter_appointments_description_and_more.cpython-311.pyc
|   |           0090_doctordetails_experiencesummary.cpython-311.pyc
|   |           0091_doctordetails_researchpapers.cpython-311.pyc
|   |           0092_doctorreviews.cpython-311.pyc
|   |           0093_patientdetails_sociallogin.cpython-311.pyc
|   |           0094_doctorconsent.cpython-311.pyc
|   |           0095_cuuser_city.cpython-311.pyc
|   |           0096_cuuser_country.cpython-311.pyc
|   |           0097_appointmentconsent_patientconsent_doctorform_and_more.cpython-311.pyc
|   |           0098_alter_patient_medical_records_report_file.cpython-311.pyc
|   |           0099_doctordetails_expertrole_and_more.cpython-311.pyc
|   |           0100_remove_doctordetails_expertrole.cpython-311.pyc
|   |           0101_alter_expertblogs_blogimages.cpython-311.pyc
|   |           0102_alter_doctordetails_otherachievements.cpython-311.pyc
|   |           0103_doctorreviews_reviewrating.cpython-311.pyc
|   |           0104_schedulerslots_status.cpython-311.pyc
|   |           0105_refundedpayments_alter_schedulerslots_status.cpython-311.pyc
|   |           0106_refundedpayments_refundobject.cpython-311.pyc
|   |           0107_alter_refundedpayments_refundobject.cpython-311.pyc
|   |           0108_appointmentmgmt_appointmentreason_and_more.cpython-311.pyc
|   |           0109_alter_doctorreviews_patientemail.cpython-311.pyc
|   |           0110_alter_appointments_status.cpython-311.pyc
|   |           0111_notificationtype.cpython-311.pyc
|   |           0112_alter_appointments_status.cpython-311.pyc
|   |           0113_alter_expertblogs_blogimages.cpython-311.pyc
|   |           0114_alter_expertblogs_blogimages.cpython-311.pyc
|   |           0115_alter_expertblogs_blogimages.cpython-311.pyc
|   |           0116_alter_refundedpayments_refundobject.cpython-311.pyc
|   |           0117_alter_expertblogs_blogimages_and_more.cpython-311.pyc
|   |           0118_alter_expertblogs_blogimages.cpython-311.pyc
|   |           0119_alter_schedulerslots_options.cpython-311.pyc
|   |           0120_alter_schedulerslots_options_and_more.cpython-311.pyc
|   |           0121_admindetails.cpython-311.pyc
|   |           0122_doctorconsent_status.cpython-311.pyc
|   |           0123_alter_doctorconsent_status.cpython-311.pyc
|   |           0124_podcast.cpython-311.pyc
|   |           0125_admindetails_phoneverified.cpython-311.pyc
|   |           0126_alter_patient_medical_records_generation_date.cpython-311.pyc
|   |           0127_reporttype.cpython-311.pyc
|   |           0128_doctorreviews_reviewstatus.cpython-311.pyc
|   |           0129_alter_doctordetails_intvideourl.cpython-311.pyc
|   |           0129_meetingsession_doctorallowpatient.cpython-311.pyc
|   |           0130_merge_20240601_1617.cpython-311.pyc
|   |           0131_alter_meetingsession_doctorallowpatient.cpython-311.pyc
|   |           0132_alter_meetingsession_meetingstatus.cpython-311.pyc
|   |           0133_doctordetails_dateofactivation.cpython-311.pyc
|   |           0134_alter_patient_medical_records_report_file.cpython-311.pyc
|   |           0135_doctorconsent_dateofconsentform.cpython-311.pyc
|   |           0136_admincontentmanagement.cpython-311.pyc
|   |           0137_refundedpayments_appid.cpython-311.pyc
|   |           0138_refundedpayments_refundcurrency_and_more.cpython-311.pyc
|   |           0139_alter_refundedpayments_refunddate.cpython-311.pyc
|   |           0140_podcast_podcastdate.cpython-311.pyc
|   |           0141_alter_doctorreviews_reviewstatus_and_more.cpython-311.pyc
|   |           0142_podcast_thumbnailimage.cpython-311.pyc
|   |           0143_expertwallet_expertwallettransactions.cpython-311.pyc
|   |           0144_alter_expertwallet_id.cpython-311.pyc
|   |           0145_faqs_expertblogs_blogviews_podcast_podcastcategory_and_more.cpython-311.pyc
|   |           0146_expertblogs_blogcategory_expertblogs_blogsubbody_and_more.cpython-311.pyc
|   |           0147_blogcategory_podcastcategory.cpython-311.pyc
|   |           0148_expertblogs_blogbannerimage_and_more.cpython-311.pyc
|   |           0149_blogsection_podcastsection_and_more.cpython-311.pyc
|   |           0150_expertwallettransactions_cleareddate.cpython-311.pyc
|   |           0151_blogcategory_description_podcastcategory_description.cpython-311.pyc
|   |           0152_podcast_podcasttimestamp_podcast_podcasttranscript.cpython-311.pyc
|   |           0153_cuuser_country_code_cuuser_prefix_and_more.cpython-311.pyc
|   |           0154_rename_podcasturl_podcast_platforms.cpython-311.pyc
|   |           0155_alter_expertwallettransactions_transactiontype.cpython-311.pyc
|   |           0156_alter_podcast_thumbnailimage.cpython-311.pyc
|   |           0157_doctordetails_commissionpercentage_and_more.cpython-311.pyc
|   |           0158_expertrank.cpython-311.pyc
|   |           0159_updatebanner.cpython-311.pyc
|   |           0160_alter_updatebanner_updatetime.cpython-311.pyc
|   |           0161_podcast_helpfullinks.cpython-311.pyc
|   |           0162_alter_cuuser_name_alter_doctordetails_address_and_more.cpython-311.pyc
|   |           0163_randomexpert.cpython-311.pyc
|   |           0164_commontopics.cpython-311.pyc
|   |           0165_alter_expertfeedback_feedback.cpython-311.pyc
|   |           0166_expertfeedback_rating.cpython-311.pyc
|   |           0167_contentselection.cpython-311.pyc
|   |           0168_contentselection_rank.cpython-311.pyc
|   |           0169_rename_categoryreferenceid_contentselection_c_id_and_more.cpython-311.pyc
|   |           0170_alter_contentselection_rank.cpython-311.pyc
|   |           0171_videoslibrary.cpython-311.pyc
|   |           0172_videoslibrary_video_file.cpython-311.pyc
|   |           0173_remove_videoslibrary_video_file_and_more.cpython-311.pyc
|   |           0174_remove_videoslibrary_videourl_and_more.cpython-311.pyc
|   |           0175_alter_videoslibrary_videotitle_and_more.cpython-311.pyc
|   |           0176_alter_videoslibrary_videotitle.cpython-311.pyc
|   |           0177_alter_videoslibrary_videotitle.cpython-311.pyc
|   |           0178_alter_videoslibrary_videotitle_and_more.cpython-311.pyc
|   |           0179_alter_videoslibrary_videotitle_and_more.cpython-311.pyc
|   |           0180_remove_videoslibrary_videotitle_and_more.cpython-311.pyc
|   |           0181_remove_videoslibrary_video_url.cpython-311.pyc
|   |           0182_alter_videoslibrary_video_file_and_more.cpython-311.pyc
|   |           0183_expertbankdetails.cpython-311.pyc
|   |           0184_alter_expertbankdetails_account_holder.cpython-311.pyc
|   |           0185_videoslibrary_isurl.cpython-311.pyc
|   |           0186_videoslibrary_thumbnail_image.cpython-311.pyc
|   |           0187_alter_videoslibrary_video_file.cpython-311.pyc
|   |           0188_usersubscription.cpython-311.pyc
|   |           0189_remove_usersubscription_interests.cpython-311.pyc
|   |           0190_alter_videoslibrary_thumbnail_image.cpython-311.pyc
|   |           0191_alter_usersubscription_is_subscribed.cpython-311.pyc
|   |           0192_remove_expertbankdetails_account_number_and_more.cpython-311.pyc
|   |           0193_usersubscription_unsubscribe_reason.cpython-311.pyc
|   |           0194_usersubscription_phone.cpython-311.pyc
|   |           0195_usersubscription_c_code.cpython-311.pyc
|   |           0196_alter_usersubscription_c_code.cpython-311.pyc
|   |           0197_medicalspecialty.cpython-311.pyc
|   |           0198_cuuserphoneotp_tempcountrycode_and_more.cpython-311.pyc
|   |           0199_alter_cuuserphoneotp_phoneotp.cpython-311.pyc
|   |           0200_cuuserphoneotp_tempuserdata.cpython-311.pyc
|   |           0201_remove_cuuserphoneotp_tempcountrycode_and_more.cpython-311.pyc
|   |           0202_cuuserphoneotp_tempcountrycode_and_more.cpython-311.pyc
|   |           0203_cuuserphoneotp_otpexpired.cpython-311.pyc
|   |           0204_remove_medicalspecialty_terms_cuuser_embedding_and_more.cpython-311.pyc
|   |           0205_cuuserphoneotp_hashedpassword.cpython-311.pyc
|   |           0206_remove_cuuser_embedding_doctordetails_embedding.cpython-311.pyc
|   |           0207_remove_doctordetails_embedding_embedding.cpython-311.pyc
|   |           0208_patientpayment_testmodel_delete_patientpayments.cpython-311.pyc
|   |           0209_delete_testmodel.cpython-311.pyc
|   |           0210_alter_expertwallettransactions_cleareddate.cpython-311.pyc
|   |           0211_patientpayment_raw_status_and_more.cpython-311.pyc
|   |           0212_refundedpayments_refundid.cpython-311.pyc
|   |           __init__.cpython-311.pyc
|   |           
|   +---models
|   |   |   doctor_models.py
|   |   |   misc_models.py
|   |   |   patient_models.py
|   |   |   __init__.py
|   |   |   
|   |   \---__pycache__
|   |           doctor_models.cpython-311.pyc
|   |           misc_models.cpython-311.pyc
|   |           patient_models.cpython-311.pyc
|   |           __init__.cpython-311.pyc
|   |           
|   +---services
|   |   |   airwallex.py
|   |   |   __init__.py
|   |   |   
|   |   \---__pycache__
|   |           airwallex.cpython-311.pyc
|   |           __init__.cpython-311.pyc
|   |           
|   +---templates
|   |       payment_processing.html
|   |       
|   +---utils
|   |   |   helper_functions.py
|   |   |   nlp_utils.py
|   |   |   pdf_utils.py
|   |   |   refund_utils.py
|   |   |   sementic_search.py
|   |   |   __init__.py
|   |   |   
|   |   \---__pycache__
|   |           helper_functions.cpython-311.pyc
|   |           nlp_utils.cpython-311.pyc
|   |           pdf_utils.cpython-311.pyc
|   |           refund_utils.cpython-311.pyc
|   |           sementic_search.cpython-311.pyc
|   |           __init__.cpython-311.pyc
|   |           
|   +---views
|   |   |   aws_views.py
|   |   |   doctor_views.py
|   |   |   misc_views.py
|   |   |   patient_views.py
|   |   |   payments_views.py
|   |   |   scheduler_views.py
|   |   |   sendgridtest.py
|   |   |   __init__.py
|   |   |   
|   |   \---__pycache__
|   |           aws_views.cpython-311.pyc
|   |           doctor_views.cpython-311.pyc
|   |           misc_views.cpython-311.pyc
|   |           patient_views.cpython-311.pyc
|   |           payments_views.cpython-311.pyc
|   |           scheduler_views.cpython-311.pyc
|   |           sendgridtest.cpython-311.pyc
|   |           __init__.cpython-311.pyc
|   |           
|   \---__pycache__
|           admin.cpython-311.pyc
|           apps.cpython-311.pyc
|           backends.cpython-311.pyc
|           cu_library.cpython-311.pyc
|           forms.cpython-311.pyc
|           middleware.cpython-311.pyc
|           serializers.cpython-311.pyc
|           urls.cpython-311.pyc
|           __init__.cpython-311.pyc
|           
\---cu_project
    |   asgi.py
    |   settings.py
    |   urls.py
    |   wsgi.py
    |   __init__.py
    |   
    +---common
    |   |   utils.py
    |   |   __init__.py
    |   |   
    |   \---__pycache__
    |           utils.cpython-311.pyc
    |           __init__.cpython-311.pyc
    |           
    +---media
    |       2024-03-27-05-23-10-IB78636PX7_drnawaz (1).jpg
    |       dummy.pdf
    |       logo.png
    |       sample.mp4
    |       sample_0iVhUez.mp4
    |       sample_5BFOmBd.mp4
    |       sample_B6JyVnU.mp4
    |       sample_bOCSEX0.mp4
    |       sample_kRUTMkX.mp4
    |       sample_KtrxYhx.mp4
    |       sample_Lmnj8fH.mp4
    |       sample_mpiq2yp.mp4
    |       sample_q7hyknK.mp4
    |       sample_RXTvvYC.mp4
    |       sample_WTP4jsM.mp4
    |       sample_yzMqIvw.mp4
    |       sample_zUKIqZ6.mp4
    |       
    \---__pycache__
            settings.cpython-311.pyc
            urls.cpython-311.pyc
            wsgi.cpython-311.pyc
            __init__.cpython-311.pyc
            
