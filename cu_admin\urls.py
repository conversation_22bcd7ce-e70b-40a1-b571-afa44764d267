from django.urls import path, include, re_path
from cu_app.views.misc_views import *
from cu_app.views.doctor_views import *
from cu_app.views.patient_views import *
from cu_app.views.scheduler_views import *
from cu_app.views.aws_views import *
# from cu_app.views.admin_views import *
from .views import *
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)
from fcm_django.api.rest_framework import FCMDeviceAuthorizedViewSet, FCMDeviceViewSet
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register('devices', FCMDeviceAuthorizedViewSet)

urlpatterns = [
    path('update-expert-status/<int:id>/', UpdateExpertStatus.as_view(),
         name="update-expert-status-path"),
    path('expert-commission/<int:DoctorId_id>/',
         ExpertCommission.as_view(), name='expert-commission_path'),
    path('get-experts-by-status-type/<str:approval>/<str:user_type>/',
         GetExpertsByStatusTypeView.as_view(), name='get-experts-by-status-path'),
    path('get-users/<int:id>/', GetPatientsView.as_view(),
         name='get-patients-by-expert-path'),
    path('get-patients-by-type/', GetPatientsByTypeView.as_view(),
         name='get-patients-by-type-path'),
    # path('get-experts-by-type/', GetEpertsByTypeView.as_view(), name='token_obtain_pair'),
    path('approve-blog/<int:id>/', ApproveBlogView.as_view(),
         name='approve-blog_path'),
    path('get-approve-doctor-consent/<str:DoctorId_id>/',
         GetApproveDoctorConsentView.as_view(), name='approve-doctor-consent-path'),
    path('stripe-test/create-product/',
         CreateProdView.as_view(), name='create_prod_path'),
    path('stripe-test/set-price/', SetPriceView.as_view(), name='set_price_path'),
    path('stripe-test/create-p-link/',
         CreatePLinkView.as_view(), name='create-p-link_path'),
    path('stripe-test/get-paymentintents/',
         PaymentIntentsView.as_view(), name='get-paymentintents_path'),
    path('update-introvideo-status/<str:DoctorId>/',
         UpdateIntroVideoStatus.as_view(), name="update-introvideo-status"),
    path('update-cancel-appointment-status/<int:id>/',
         UpdateCancelAppointmentStatus.as_view(), name="update-cancel-appointment-status"),
    # path('get-notifications/', GetNotifications.as_view(), name="get-notifications"),
    path('notification-status/<str:id>/',
         ListUpdateNotificationStatus.as_view(), name="notification-status"),
    path('appointment-filter/', AppointmentsFilter.as_view(),
         name="appointment-filter"),
    path('stripe-test/get-balance/',
         GetBalanceView.as_view(), name="get-balance_path"),
    path('add-child-admin/', AddChildAdmin.as_view(),
         name="add-child-admin-path"),
    path('verify-mobile/', VerifyMobileAPIView.as_view(),
         name="verify-mobile-path"),
    path("publish_podcast/<int:id>/", PublishPodcastView.as_view(),
         name="publish_podcast_path"),
    # push notifications
    path('pushnotific-test/', PushNotificTestView.as_view(),
         name='pushnotific-test_path'),
    path('pushnotific-test1/', PushNotificTestView1.as_view(),
         name='pushnotific1-test_path'),
    # added
    path('admin-homepage/', HomepageView.as_view(), name='homepage_path'),
    path('search-user-homepage/', HomepageSearchBarUser.as_view(),
         name='homepage_search_bar_user_path'),
    path('filter-appointment-homepage/', HomepageFilterAppointmentUser.as_view(),
         name='homepage_appointment_filter_path'),
    path('get-edit-permissions/<int:AdminId_id>/', GetEditChildAdminPermissionView.as_view(),
         name='get-edit-child-admin-permission_path'),
    path('create-list-permission/', CreateListPermissionsView.as_view(),
         name='create-list-permission_path'),
    path('update-single-permission/<str:id>/', EditPermissionView.as_view(),
         name='update_single_permission'),
    path('expert-dues/<int:DoctorId>/',
         ExpertPaymentPage.as_view(), name='expert-payment_path'),
    path('create-report-type/', CreateReportTypeView.as_view(),
         name="create_report_type_path"),
    path('get-update-report-type/<str:id>/',
         GetUpdateReportTypeView.as_view(), name="get_update_report_type_path"),
    path('create-content-type/', CreateContentTypeView.as_view(),
         name="create_content_type_path"),
    path('get-update-content-type/<str:id>/',
         GetUpdateContentTypeView.as_view(), name="get_update_content_type_path"),
    path('approve-expert-review/<str:id>/', GetApproveExpertReviewViews.as_view(),
         name="get_approve_expert_reviews_path"),
    path('update_user_profile/<str:email>/',
         UserUpdate.as_view(), name="user_update_path"),
    path('delete_user_profile/<str:email>/',
         UserDeleteAPIView.as_view(), name="delete_user_profile_path"),
    path('get-update-expertise/<str:id>/',
         GetUpdateExpertiseView.as_view(), name="get-update-expertise-path"),
    path('expert_payment/<str:expert_id>/<str:transaction_id>/',
         GetUpdateExpertPaymentView.as_view(), name="get-update-expert-payment-path"),
    path('get-update-delete-faq/<str:id>/',
         GetUpdateDeleteFAQsView.as_view(), name="get-update-delete-faq-path"),
    path('get-update-blog-category/<str:id>/',
         GetUpdateBlogCategoryView.as_view(), name="get-update-blog-category-path"),
    path('get-update-podcast-category/<str:id>/',
         GetUpdatePodcastCategoryView.as_view(), name="get-update-podcast-category-path"),
    path('update-section/<str:id>/', UpdateRankingBlogPodcastView.as_view(),
         name="update-podcast-blog-section-path"),
    path('list-update-top-author/<str:id>/',
         ListUpdateTopAuthorView.as_view(), name="expert_rank-path"),
    path('create-top-author/', CreateTopAuthorView.as_view(),
         name="create_expert_rank-path"),
    path('create-banner/', CreateBannerView.as_view(), name="create_banner-path"),
    path('list-update-banner/<str:id>/',
         ListUpdateBannerView.as_view(), name="update_banner-path"),
    path('list-update-random-expert/<str:id>/',
         ListUpdateRandomExpertView.as_view(), name="random_expert-path"),
    path('create-random-expert/', CreateRandomExpertView.as_view(),
         name="create_random_expert-path"),
    path('select-content/', SelectContentView.as_view(),
         name="select_content-path"),
    path('delete-content/<int:id>/', DeleteContentView.as_view(),
         name="delete_content-path"),
    path('add-common-topics/', CreateCommonTopicsView.as_view(),
         name="create_common_topics-path"),
    path('list-update-common-topics/<str:id>/',
         ListUpdateCommonTopicsView.as_view(), name="update_common_topics-path"),
    path('list-update-common-topics/<str:id>/',
         ListUpdateCommonTopicsView.as_view(), name="update_common_topics-path"),
    path("videos-library/", VideosLibraryView.as_view(), name="videos-library"),
    path("videos-library/<str:id>/", VideosLibraryView.as_view(),
         name="delete-videos-library"),
    path("update-videos-library/<str:id>/", UpdateVideosLibraryView.as_view(),
         name="update-videos-library"),
    path("subscribed-users/", SubscribedUserGetView.as_view(),
         name="subscribed-users"),
    path("subscribed-users/<str:id>/", SubscribedUserGetView.as_view(),
         name="single-subscribed-users"),

    # test notifications
    path('test-notification/', TestNotificationView.as_view(), name='test-notification'),
    # test notifications

    path('api/', include(router.urls)),
]
