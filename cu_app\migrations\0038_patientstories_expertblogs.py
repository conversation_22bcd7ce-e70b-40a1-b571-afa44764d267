# Generated by Django 4.2.5 on 2023-12-11 12:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0037_doctordetails_emailcodegentime_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientStories',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('DoctorRecommendation', models.IntegerField(blank=True, null=True)),
                ('CancerTreatmentType', models.CharField(blank=True, max_length=100, null=True)),
                ('WaitingTime', models.CharField(blank=True, max_length=100, null=True)),
                ('Improvement', models.CharField(blank=True, max_length=100, null=True)),
                ('Rating', models.IntegerField(blank=True, null=True)),
                ('ExperienceSummary', models.TextField(blank=True, null=True)),
                ('Anonymousity', models.IntegerField(blank=True, null=True)),
                ('ApptId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
            ],
        ),
        migrations.CreateModel(
            name='ExpertBlogs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('BlogTitle', models.CharField(blank=True, max_length=100, null=True)),
                ('BlogBody', models.TextField(blank=True, null=True)),
                ('BlogDateTime', models.DateTimeField(auto_now_add=True)),
                ('BlogImages', models.CharField(blank=True, max_length=500, null=True)),
                ('ExpertId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
