from rest_framework import serializers
from cu_app.models import *
from django.contrib.auth import get_user_model


class CuUserRegisterSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = "__all__"
        ref_name = "CuAppUserRegistration"

    def validate_email(self, value):
        return value.strip().lower()

    # def create(self, validated_data):
    #     validated_data["email"] = validated_data["email"].strip().lower()
    #     password = validated_data.pop("password", None)

    #     user = CuUser(**validated_data)

    #     # Password is already hashed before this point — just assign
    #     if password:
    #         user.password = password

    #     user.save()
    #     return user

    def create(self, validated_data):
        print("validated_data in the serializer", validated_data)
        validated_data["email"] = validated_data["email"].strip().lower()
        user = CuUser.objects.create_user(
            validated_data["name"],
            validated_data["phone"],
            validated_data["email"],
            validated_data["password"],
            country_code=validated_data.get("country_code"),
            # validated_data["country_code"],
            # validated_data['TimeZone'],
        )
        return user


class CuUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        exclude = [
            "is_admin",
            "is_active",
            "is_superuser",
            "password",
            "user_permissions",
            "groups",
            "PWVerifyCode",
            "PWCodeGentime",
        ]
        ref_name = "CuAppUserSerializer"


class ExpertiseCancertypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertiseCancertype
        # fields = "__all__"
        exclude = ['embedding']
        ref_name = "CuAppExpertiseCancerType"


class patient_medical_recordsSerializer(serializers.ModelSerializer):
    class Meta:
        model = patient_medical_records
        fields = "__all__"


class SlotSerializer(serializers.ModelSerializer):
    class Meta:
        model = SchedulerSlots
        fields = "__all__"
        ref_name = "CuAppSlotSerializer"


class AppointmentsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Appointments
        fields = "__all__"
        ref_name = "CuAppAppointmentsSerializer"


class testjsonSerializer(serializers.ModelSerializer):
    class Meta:
        model = testjson
        fields = "__all__"


class DoctorDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorDetails
        # fields = '__all__'
        exclude = ["EmailCodeGentime", "EmailVerifyCode"]
        ref_name = "CuAppDoctorDetails"


class PrescriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Prescription
        fields = "__all__"


class OralMedicationSerializer(serializers.ModelSerializer):
    class Meta:
        model = OralMedication
        fields = "__all__"


class IVIMMedicationSerializer(serializers.ModelSerializer):
    class Meta:
        model = IVIMMedication
        fields = "__all__"


class PatientDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = PatientDetails
        # fields = '__all__'
        exclude = ["EmailVerifyCode", "EmailCodeGentime"]
        ref_name = "CuAppPatientDetails"


class PatientQueriesSerializer(serializers.ModelSerializer):
    class Meta:
        model = PatientQueries
        fields = "__all__"


class MeetingSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = MeetingSession
        fields = "__all__"


class ExpertBlogsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertBlogs
        fields = "__all__"
        ref_name = "CuAppExpertBlogs"


class CuUserPhoneOTPSerializer(serializers.ModelSerializer):
    class Meta:
        model = CuUserPhoneOTP
        fields = "__all__"


class PatientStoriesSerializer(serializers.ModelSerializer):
    class Meta:
        model = PatientStories
        fields = "__all__"


class StatusReasonSerializer(serializers.ModelSerializer):
    class Meta:
        model = StatusReason
        fields = "__all__"


class AppointmentConsentSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppointmentConsent
        fields = "__all__"


class ExpertFeedbackSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertFeedback
        fields = "__all__"


class PricingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Pricing
        fields = "__all__"


class IRPrescriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = IRPrescription
        fields = "__all__"


class TicketSerializer(serializers.Serializer):

    id = serializers.CharField()
    ticketNumber = serializers.CharField()
    email = serializers.CharField()
    status = serializers.CharField()
    priority = serializers.CharField()
    subject = serializers.CharField()


class TicketThreadSerializer(serializers.Serializer):

    summary = serializers.CharField()
    author_name = serializers.CharField()
    author_photo = serializers.CharField()
    CreatedTime = serializers.CharField()
    to = serializers.CharField()


class PatientPaymentsSerializer(serializers.ModelSerializer):
    appointment_details = serializers.SerializerMethodField()

    class Meta:
        model = PatientPayment
        fields = "__all__"

    def get_appointment_details(self, obj):
        return {
            'id': obj.appointment.id,
            'date': obj.appointment.slot_id.schedule_start_time,
            'doctor': obj.appointment.slot_id.doctor.name,
            'status': obj.appointment.status
        }


class DoctorReviewsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorReviews
        fields = "__all__"
        ref_name = "CuAppDoctorReviews"


class DoctorConsentSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorConsent
        fields = "__all__"
        ref_name = "CuAppDoctorConsent"


class PushNotificationsSerializer(serializers.ModelSerializer):
    class Meta:
        model = PushNotifications
        fields = "__all__"
        ref_name = "CuAppPushNotification"


class NotificationTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationType
        fields = "__all__"
        ref_name = "CuAppNotificationType"


# added


class AdminDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminDetails
        fields = "__all__"
        ref_name = "CuAppAdminDetails"
        # exclude=['EmailVerifyCode', 'EmailCodeGentime']


# added


class PodcastSerializer(serializers.ModelSerializer):
    class Meta:
        model = Podcast
        fields = "__all__"
        ref_name = "CuAppPodcast"


class ReportTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReportType
        fields = "__all__"
        ref_name = "CuAppReportType"


class ContentTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminContentManagement
        fields = "__all__"
        ref_name = "CuAppContentType"


class ExpertWalletTransactionsSerializer(serializers.ModelSerializer):

    class Meta:
        model = ExpertWalletTransactions
        fields = "__all__"
        ref_name = "CuAppExpertWalletTransactions"
        # depth = 1


class FAQsSerializer(serializers.ModelSerializer):
    class Meta:
        model = FAQs
        fields = "__all__"
        ref_name = "CuAppFAQs"


class BlogCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogCategory
        fields = "__all__"
        ref_name = "CuAppBlogCategory"


class PodcastCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PodcastCategory
        fields = "__all__"
        ref_name = "CuAppPodcastCategory"


class PodcastSectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PodcastSection
        fields = "__all__"
        ref_name = "CuAppPodcastSection"


class BlogSectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogSection
        fields = "__all__"
        ref_name = "CuAppBlogSection"


class BlogSectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogSection
        fields = "__all__"
        ref_name = "CuAppBlogSection"


# ------------update_banner---------------------
class UpdateBannerSerializer(serializers.ModelSerializer):
    class Meta:
        model = UpdateBanner
        fields = "__all__"
        ref_name = "CuAppUpdateBanner"


class CommonTopicsSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommonTopics
        fields = "__all__"
        ref_name = "CuAppCommonTopics"


class VideosLibrarySerializer(serializers.ModelSerializer):
    class Meta:
        model = VideosLibrary
        fields = "__all__"
        ref_name = "CuVideosLibrary"


class ExpertBankDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertBankDetails
        fields = '__all__'
        ref_name = "ExpertBankDetails"


class UserSubscriptionSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(required=True)
    name = serializers.CharField(required=True)
    phone = serializers.CharField(required=True)

    class Meta:
        model = UserSubscription
        ref_name = 'UserSubscription'
        fields = '__all__'
        read_only_fields = ['subscribed_at']

    def validate_email(self, value):
        if UserSubscription.objects.filter(email=value).exists():
            raise serializers.ValidationError(
                "This email is already subscribed.")
        return value

    def validate_phone(self, value):
        if UserSubscription.objects.filter(phone=value).exists():
            raise serializers.ValidationError(
                "This phone number is already subscribed.")
        return value


class MedicalSpecialtySerializer(serializers.ModelSerializer):
    class Meta:
        model = MedicalSpecialty
        fields = "__all__"

    def validate_terms(self, terms):
        """Ensure terms list does not contain duplicates"""
        if len(terms) != len(set(terms)):
            raise serializers.ValidationError(
                "Duplicate terms are not allowed.")
        return terms
