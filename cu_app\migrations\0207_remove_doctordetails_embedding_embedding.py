# Generated by Django 4.2.5 on 2025-04-28 10:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0206_remove_cuuser_embedding_doctordetails_embedding'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='doctordetails',
            name='embedding',
        ),
        migrations.CreateModel(
            name='Embedding',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('embedding', models.J<PERSON>NField()),
                ('embedding_type', models.CharField(default='doctor_profile', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='embedding', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
