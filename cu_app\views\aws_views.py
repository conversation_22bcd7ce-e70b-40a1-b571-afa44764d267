from __future__ import print_function
from django.shortcuts import render
from rest_framework import generics,status
from django.middleware.csrf import get_token
from django.http import JsonResponse
from ..serializers import *
from datetime import *
from rest_framework.response import Response
import json
from django.contrib.auth.models import Group, Permission
from django.views import View
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import authenticate,login,logout,get_user_model
from ..models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

import datetime
import os.path

import re
from datetime import datetime,date, timedelta,timezone
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from ..forms import *
from ..cu_library import *
#aws sns
import logging
import time
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from rest_framework.parsers import JSONParser
import json

load_dotenv()

logger = logging.getLogger(__name__)

class SnsWrapper:
    """Encapsulates Amazon SNS topic and subscription functions."""
    def __init__(self, sns_resource):
        """
        :param sns_resource: A Boto3 Amazon SNS resource.
        """
        self.sns_resource = sns_resource

    def publish_text_message(self, phone_number, message):
        """
        Publishes a text message directly to a phone number without need for a
        subscription.

        :param phone_number: The phone number that receives the message. This must be
                             in E.164 format. For example, a United States phone
                             number might be +12065550101.
        :param message: The message to send.
        :return: The ID of the message.
        """
        try:
            response = self.sns_resource.meta.client.publish(
                PhoneNumber=phone_number, Message=message)
            message_id = response['MessageId']
            logger.info("Published message to %s.", phone_number)
            print(f"Published message to {phone_number}")
        except ClientError:
            logger.exception("Couldn't publish message to %s.", phone_number)
            print(f"Couldn't publish message to {phone_number}")
            raise
        else:
            return message_id


@method_decorator(csrf_exempt,name="dispatch")
class SendOtp(View):
    def post(self,r):

        # print("sending sms otp")
        # #logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
        #
        # sns_wrapper = SnsWrapper(boto3.resource('sns'))
        # req_data=json.loads(r.body.decode("utf-8"))
        # print(f"req dattaaaa----{req_data}--{type(req_data)}")
        # phone_number = str(req_data['phonenumber'])
        # otp=str(req_data['otp'])
        # sns_res=None
        # if phone_number != '':
        #
        #     sns_res=sns_wrapper.publish_text_message(phone_number, otp)
        #     return JsonResponse({"message": "otp sent successfully11"})
        #     #print(f"otp sent successfully!--{sns_res}--{type(sns_res)}")

        #another way

        # sns_client = boto3.client('sns')
        # smsattrs = {
        #     'AWS.SNS.SMS.SenderID': {'DataType': 'String', 'StringValue': 'TestSender'},
        #     'AWS.SNS.SMS.SMSType': {'DataType': 'String', 'StringValue': 'Transactional'}
        # }

        sns_client = boto3.client(
            "sns",
            aws_access_key_id="********************",
            aws_secret_access_key="y42NwjBkqPfbqxzszlGdYZSWp4i5A5zfms5QYHWA",
            region_name="ap-south-1"
        )

        sns_res = sns_client.publish(
            PhoneNumber='+917457891710',
            Message='This is a test SMS message11',
            # TopicArn='string', (Optional - can't be used with PhoneNumer)
            # TargetArn='string', (Optional - can't be used with PhoneNumer)
            # Subject='string', (Optional - not used with PhoneNumer)
            # MessageStructure='string' (Optional)
            #MessageAttributes=smsattrs
        )

        print("resss----",sns_res)
        if sns_res:
            return JsonResponse({"message": "otp sent successfully"})
        else:
            return JsonResponse({"message": "otp could'nt be sent"})