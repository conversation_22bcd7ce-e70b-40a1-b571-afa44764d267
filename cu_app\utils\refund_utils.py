from decimal import Decimal
from cu_app.models.misc_models import AdminContentManagement
from django.utils import timezone


def calculate_refund_percentage(cancellation_time, appointment_time):
    """Calculates refund percentage based on cancellation policy (cut percentage)."""
    try:
        # Get the current timezone
        local_tz = timezone.get_current_timezone()

        # Ensure both times are timezone-aware
        if not cancellation_time.tzinfo:
            cancellation_time = timezone.make_aware(cancellation_time, local_tz)
        if not appointment_time.tzinfo:
            appointment_time = timezone.make_aware(appointment_time, local_tz)

        # Convert to current timezone
        cancellation_local = cancellation_time.astimezone(local_tz)
        appointment_local = appointment_time.astimezone(local_tz)

        # Calculate time difference in hours
        time_diff = appointment_local - cancellation_local
        hours_before = time_diff.total_seconds() / 3600  

        # Fetch cancellation policies sorted by time (ascending)
        policies = AdminContentManagement.objects.filter(
            Category="Cancellation Refund Policy").order_by('Content__Time')

        if not policies.exists():
            return Decimal('1')  # No policies = full refund

        # Find the applicable policy
        for policy in policies:
            policy_time = float(policy.Content['Time'])
            if hours_before < policy_time:
                cut_percentage = Decimal(policy.Content['Refund'])
                refund_percentage = (Decimal('100') - cut_percentage) / Decimal('100')
                return refund_percentage

        # If no policy matches (hours_before >= max policy time), return 100% refund
        return Decimal('1')

    except Exception as e:
        return Decimal('1')  # Default to full refund on error
    

