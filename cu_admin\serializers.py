from django.contrib.auth.hashers import make_password
from rest_framework import serializers
from cu_app.models import *
from django.contrib.auth import get_user_model
from .models import *
from django.contrib.auth.models import Permission


class CuUserRegisterSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = '__all__'
        ref_name = 'CuAdminRegisterSerializer'

    # def create(self, validated_data):
    #     validated_data["email"] = validated_data["email"].strip().lower()
    #     password = validated_data.pop("password", None)

    #     user = CuUser(**validated_data)

    #     # Use Django's secure password setter
    #     if password:
    #         user.set_password(password)  # ✅ This is the correct way

    #     user.save()
    #     return user

    def create(self, validated_data):
        print(f"validated dataaaaaaaa {validated_data}")
        user = CuUser.objects.create_user(
            validated_data['name'],
            validated_data['phone'],
            validated_data['email'],
            validated_data['password'],
            validated_data['country_code'],
            # validated_data['TimeZone'],
        )
        return user


class CuUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = '__all__'
        ref_name = 'CuAdminUserSerializer'


class CuUserFilterSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        exclude = ["is_admin", "is_superuser",
                   "password", "user_permissions", "groups"]
        ref_name = 'CuAdminUserFilterSerializer'


class PatientDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = PatientDetails
        exclude = ["id", "PatientId"]
        ref_name = 'CuAdminPatientDetails'


class ExpertBlogsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertBlogs
        fields = '__all__'
        ref_name = 'CuAdminExpertBlogs'


class TimezoneTestModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = TimezoneTestModel
        fields = '__all__'


class DoctorDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorDetails
        fields = '__all__'
        ref_name = 'CuAdminDoctorDetails'


class ExpertiseCancertypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertiseCancertype
        fields = '__all__'
        ref_name = 'CuAdminExpertiseCancerType'

# added


class AppointmentsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Appointments
        fields = '__all__'
        ref_name = 'CuAdminAppointments'


class NotificationTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationType
        fields = '__all__'
        ref_name = 'CuAdminNotificationType'


class SlotSerializer(serializers.ModelSerializer):
    class Meta:
        model = SchedulerSlots
        fields = '__all__'


class DoctorConsentSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorConsent
        fields = '__all__'
        ref_name = 'CuAdminDoctorConsent'
# added


class PodcastSerializer(serializers.ModelSerializer):
    class Meta:
        model = Podcast
        fields = '__all__'
        ref_name = 'CuAdminPodcast'


class AppointmentConsentSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppointmentConsent
        fields = '__all__'
        ref_name = 'CuAdminAppointmentConsent'


class MeetingSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = MeetingSession
        fields = '__all__'
        ref_name = 'CuAdminMeetingSession'

# added


class ContentTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminContentManagement
        fields = '__all__'
        ref_name = 'CuAdminContentType'


class ReportTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReportType
        fields = '__all__'
        ref_name = 'CuAdminReportType'


class DoctorReviewsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorReviews
        fields = '__all__'
        ref_name = 'CuAdminDoctorReviews'


class AdminDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminDetails
        fields = '__all__'
        ref_name = 'CuAdminAdminDetails'
        # exclude=['EmailVerifyCode', 'EmailCodeGentime']

# added


class ExpertWalletTransactionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertWalletTransactions
        fields = '__all__'
        ref_name = 'CuAdminExpertWalletTransactions'


class FAQsSerializer(serializers.ModelSerializer):
    class Meta:
        model = FAQs
        fields = '__all__'
        ref_name = 'CuAdminFAQs'


class BlogCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogCategory
        fields = '__all__'
        ref_name = 'CuAdminBlogCategory'


class PodcastCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PodcastCategory
        fields = '__all__'
        ref_name = 'CuAdminPodcastCategory'


class ExpertRankSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertRank
        fields = '__all__'


class UpdateBannerSerializer(serializers.ModelSerializer):
    class Meta:
        model = UpdateBanner
        fields = '__all__'


class RandomExpertSerializer(serializers.ModelSerializer):
    class Meta:
        model = RandomExpert
        fields = '__all__'


class CommonTopicsSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommonTopics
        fields = '__all__'
        ref_name = 'CuAdminCommonTopics'


class ContentSelectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentSelection
        fields = '__all__'
        ref_name = 'CuAdminContentSelection'


class VideosLibrarySerializer(serializers.ModelSerializer):
    class Meta:
        model = VideosLibrary
        fields = "__all__"
        ref_name = "CuVideosLibraryAdmin"

    def validate(self, attrs):
        is_url = attrs.get("isUrl")
        video_file = attrs.get("video_file")

        if str(is_url) == "True" and not video_file:
            raise serializers.ValidationError(
                "A valid URL must be provided when isUrl is True."
            )
        elif str(is_url) == "False" and not video_file:
            raise serializers.ValidationError(
                "A video file must be uploaded when isUrl is False."
            )

        return attrs

    def create(self, validated_data):
        return super().create(validated_data)


class UserSubscriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserSubscription
        ref_name = 'UserSubscriptionAdmin'
        fields = "__all__"
        read_only_fields = ['subscribed_at']

class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type']
        
class PrescriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Prescription
        fields = '__all__'        
        
class IRPrescriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = IRPrescription
        fields = '__all__'                