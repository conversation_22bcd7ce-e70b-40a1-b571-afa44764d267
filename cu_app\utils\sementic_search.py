import openai
import os
import re
import numpy as np
from cu_admin.serializers import CuUser<PERSON>erializer, DoctorDetailsSerializer
# from cu_app.views.patient_views import *
from cu_app.models.doctor_models import *
from cu_app.cu_library import get_s3_signed_url_bykey
from cu_app.utils.helper_functions import *
from cu_app.models.patient_models import Appointments

openai.api_key = os.getenv("OPENAI_KEY")

# def get_embedding(text, model="text-embedding-3-large"):


def get_embedding(text, model="text-embedding-ada-002"):
    if not text:
        return None
    text = re.sub(r'\s+', ' ', text.lower()).strip()[:1000]
    try:
        response = openai.Embedding.create(input=[text], model=model)
        embedding = response["data"][0]["embedding"]
        # Normalize embedding for numerical stability
        norm = np.linalg.norm(embedding)
        return np.array(embedding) / norm if norm > 0 else embedding
    except Exception as e:
        print(f"Error getting embedding: {e}")
        return None


def build_detailed_doctor_data(doctor):
    # Basic serializations
    expert_role = get_cu_user_type(doctor.id)
    rank = ExpertRank.objects.filter(ExpertId_id=doctor.id).first()
    expert_details = CuUserSerializer(doctor).data
    other_details = DoctorDetailsSerializer(doctor.doctordetails).data

    # Process media URLs
    for field in ["ProfilePhoto", "Signature"]:
        if other_details.get(field):
            other_details[field] = get_s3_signed_url_bykey(
                other_details[field])

    if other_details.get("IntVideoUrl"):
        other_details["IntVideoUrl"] = [
            get_s3_signed_url_bykey(url) for url in other_details["IntVideoUrl"]
        ]

    # Calculate ratings
    apps = Appointments.objects.filter(slot_id__doctor=doctor.id)
    stories = [story for app in apps for story in app.patientstories_set.all()]
    reviews = doctor.doctorreviews_set.filter(ReviewStatus=2)

    stories_count = len(stories)
    reviews_count = reviews.count()

    rating_s = sum(story.Rating for story in stories) if stories else 0
    rating_r = sum(review.ReviewRating for review in reviews) if reviews else 0

    rating_in_number1 = rating_s // stories_count if stories_count else 0
    rating_in_number2 = rating_r // reviews_count if reviews_count else 0
    avg_rating = (rating_in_number1 +
                  rating_in_number2) // 2 if (stories_count + reviews_count) else 0

    # Return only required fields
    return {
        "name": expert_details.get("name"),
        "prefix": expert_details.get("prefix"),
        "city": expert_details.get("City"),
        "country": expert_details.get("Country"),
        "expert_role": expert_role,
        "patient_stories": stories_count,
        "doctor_reviews": reviews_count,
        "rating": avg_rating,
        "IntroVideoStatus": other_details.get("IntroVideoStatus"),
        "ConsultationFees": other_details.get("ConsultationFees"),
        "IntVideoUrl": other_details.get("IntVideoUrl"),
        "ProfilePhoto": other_details.get("ProfilePhoto"),
        "Experience": other_details.get("Experience"),
        "Qualifications": other_details.get("Qualifications"),
        "Dept": other_details.get("Dept"),
        "MemberCode": other_details.get("MemberCode"),
        "Summary": other_details.get("Summary"),
        "DoctorId": other_details.get("DoctorId"),
        "expertise": expert_details.get("expertise"),
        "sex": expert_details.get("sex"),
        "approval": expert_details.get("approval"),
    }
