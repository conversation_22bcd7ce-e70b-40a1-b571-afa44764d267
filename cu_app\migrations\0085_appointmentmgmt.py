# Generated by Django 4.2.5 on 2024-02-09 07:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0084_alter_doctordetails_introvideostatus'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppointmentMgmt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('EventType', models.CharField(blank=True, max_length=50, null=True)),
                ('EventDate', models.DateTimeField(default=django.utils.timezone.now)),
                ('AppId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
                ('By', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
