# Generated by Django 4.2.5 on 2024-07-24 07:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0145_faqs_expertblogs_blogviews_podcast_podcastcategory_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='expertblogs',
            name='BlogCategory',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='expertblogs',
            name='BlogSubBody',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='expertblogs',
            name='BlogSubImage',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='expertblogs',
            name='BlogSubTitle',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
    ]
