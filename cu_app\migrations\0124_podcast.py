# Generated by Django 4.2.5 on 2024-05-13 09:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0123_alter_doctorconsent_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='Podcast',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('PodcastURL', models.TextField(blank=True, null=True)),
                ('PodcastTopic', models.TextField(blank=True, null=True)),
                ('PodcastStatus', models.IntegerField(choices=[(1, 'requested'), (2, 'published')], default=1)),
                ('ExpertId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
