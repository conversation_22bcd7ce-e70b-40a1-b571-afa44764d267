from __future__ import print_function
from django.shortcuts import render
from rest_framework import generics,status
from django.middleware.csrf import get_token
from django.http import JsonResponse
from ..serializers import *
from datetime import *
from rest_framework.response import Response
import json
from django.contrib.auth.models import Group, Permission
from django.views import View
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import authenticate,login,logout,get_user_model
from ..models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

import datetime
import os.path

import re
from datetime import datetime,date, timedelta,timezone
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from ..forms import *
from ..cu_library import *
#aws sns
import logging
import time
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from rest_framework.parsers import J<PERSON><PERSON>arser
import json
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.decorators import permission_classes
from rest_framework.permissions import IsAuthenticated
import sendgrid
from sendgrid.helpers.mail import *
from python_http_client.exceptions import HTTPError

@method_decorator(csrf_exempt,name="dispatch")
class CreowizTestMail(View):
    def post(self,r):

        # zeptomail added
        payload={
            "template_key": "2518b.41c485fda42f6e5f.k1.ec531080-dc5d-11ee-96f3-52540038fbba.18e180f6188",
            #"bounce_address": "<EMAIL>",
            "from": {
                "address": "<EMAIL>",
                "name": "Cancer Unwired"
            },
            "to": [
                {
                    "email_address": {
                        "address": "<EMAIL>",
                        "name": "Tapan Kumar Sahu"
                    }
                }

            ],
            "merge_info": {
                "u_email": "<EMAIL>",
                "verify_url1":"http://www.xyz.com/"
            },
        }

        headers = {
            'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        zeptomail_url="https://api.zeptomail.in/v1.1/email/template"

        #zeptomail ends
        try:

            response = requests.request("POST", zeptomail_url, headers=headers, data=json.dumps(payload))
            print(f"statussssssssssssssss{response.status_code}")
            #print(response.body)
            print(f"headerssssssssssssss{response.content}----------{type(response)}")
            return JsonResponse({"message":"success"})
        except HTTPError as e:
            print(f"exception--------{e.to_dict}")
            return JsonResponse({"message":"failed"})