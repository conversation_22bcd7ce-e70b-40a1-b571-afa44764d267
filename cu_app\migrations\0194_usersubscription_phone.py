# Generated by Django 4.2.5 on 2025-03-13 11:26

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0193_usersubscription_unsubscribe_reason'),
    ]

    operations = [
        migrations.AddField(
            model_name='usersubscription',
            name='phone',
            field=models.CharField(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')]),
        ),
    ]
