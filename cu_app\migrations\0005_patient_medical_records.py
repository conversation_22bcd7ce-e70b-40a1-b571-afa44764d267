# Generated by Django 4.2.5 on 2023-10-11 09:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0004_alter_cuuser_expertise'),
    ]

    operations = [
        migrations.CreateModel(
            name='patient_medical_records',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reportname', models.CharField(max_length=50)),
                ('reporttype', models.CharField(max_length=50)),
                ('generation_date', models.CharField(max_length=50)),
                ('reportsummary', models.TextField()),
                ('report_file', models.CharField(max_length=150)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
