from django.db import models
from django.contrib.auth.models import (
    BaseUserManager,
    AbstractBaseUser,
    PermissionsMixin,
)
from datetime import *
from rest_framework import generics, status
from rest_framework.response import Response
from django.http import JsonResponse
from .patient_models import CuUser, Appointments
from fcm_django.models import FCMDevice
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator


class Prescription(models.Model):
    AppointmentId = models.ForeignKey(Appointments, on_delete=models.CASCADE)
    CurrentDiagnosis = models.CharField(max_length=100, blank=True, null=True)
    ExistingTreatment = models.TextField(blank=True, null=True)
    Recommendation = models.TextField(blank=True, null=True)
    SpecialInstructions = models.TextField(blank=True, null=True)
    FollowUp = models.CharField(max_length=100, blank=True, null=True)
    DoctorSignature = models.TextField(blank=True, null=True)
    DateOfCreatingPresc = models.DateField(default=date.today)


class IRPrescription(models.Model):
    AppointmentId = models.ForeignKey(Appointments, on_delete=models.CASCADE)
    CurrentDiagnosis = models.CharField(max_length=100, blank=True, null=True)
    ConsultationSummary = models.TextField(blank=True, null=True)
    Remarks = models.TextField(blank=True, null=True)
    SpecialInstructions = models.TextField(blank=True, null=True)
    FollowUp = models.CharField(max_length=100, blank=True, null=True)
    DoctorSignature = models.TextField(blank=True, null=True)
    DateOfCreatingPresc = models.DateField(default=date.today)


class OralMedication(models.Model):
    MedicineName = models.CharField(max_length=100, blank=True, null=True)
    DoseStrength = models.CharField(max_length=100, blank=True, null=True)
    Frequency = models.CharField(max_length=100, blank=True, null=True)
    Duration = models.CharField(max_length=100, blank=True, null=True)
    Remarks = models.CharField(max_length=100, blank=True, null=True)
    PrescriptionId = models.ForeignKey(Prescription, on_delete=models.CASCADE)


class IVIMMedication(models.Model):
    MedicineName = models.CharField(max_length=100, blank=True, null=True)
    DoseStrength = models.CharField(max_length=100, blank=True, null=True)
    ModeOfAdministration = models.CharField(
        max_length=100, blank=True, null=True)
    Frequency = models.CharField(max_length=100, blank=True, null=True)
    Remarks = models.CharField(max_length=100, blank=True, null=True)
    PrescriptionId = models.ForeignKey(Prescription, on_delete=models.CASCADE)


class MeetingSession(models.Model):
    AppointmentId = models.ForeignKey(Appointments, on_delete=models.CASCADE)
    SessionId = models.CharField(max_length=100, blank=True, null=True)
    MeetingId = models.CharField(max_length=100, blank=True, null=True)
    MeetingPwd = models.CharField(max_length=100, blank=True, null=True)
    IsDoctorPresent = models.BooleanField(default=False)
    IsPatientPresent = models.BooleanField(default=False)
    SessionStartTime = models.DateTimeField(null=True, blank=True)
    PatientJoinTime = models.DateTimeField(null=True, blank=True)
    PatientLeaveTime = models.DateTimeField(null=True, blank=True)
    SessionEndTime = models.DateTimeField(null=True, blank=True)
    MEETING_STATUS_CHOICES = [
        (0, "not started"),
        (1, "ongoing"),
        (2, "completed"),
        (3, "unattended"),
    ]
    MeetingStatus = models.IntegerField(
        choices=MEETING_STATUS_CHOICES,
        default=0,
    )

    MEETING_SUCCESS_CHOICES = [
        (0, "failed"),
        (1, "sucess"),
        (2, "not sure"),
    ]
    IsSuccess = models.IntegerField(
        choices=MEETING_SUCCESS_CHOICES,
        default=2,
    )
    PatientJoinApproval = models.BooleanField(default=False)
    TranscriptFile = models.CharField(max_length=100, blank=True, null=True)
    PatientRequest = models.BooleanField(default=False)
    DoctorAllowPatient = models.IntegerField(default=1)
    ExpirationTime= models.DateTimeField(null=True, blank=True)


class CuUserPhoneOTP(models.Model):
    UserEmail = models.CharField(max_length=100, blank=True, null=True)
    # PhoneOTP = models.IntegerField(blank=True, null=True)
    PhoneOTP = models.CharField(max_length=10, blank=True, null=True)
    PhoneOTPGenTime = models.DateTimeField(auto_now_add=True)
    PhoneOTPVerified = models.BooleanField(default=False)
    TempPhone = models.CharField(max_length=20, blank=True, null=True)
    TempCountryCode = models.CharField(max_length=10, blank=True, null=True)
    OTPExpired = models.BooleanField(default=False)
    HashedPassword = models.CharField(max_length=128, blank=True, null=True)

    TempUserData = models.JSONField(blank=True, null=True)


class Pricing(models.Model):
    PlatformCharges = models.IntegerField(blank=True, null=True)
    TransactionCharges = models.IntegerField(blank=True, null=True)


class S3ObjectsKeys(models.Model):
    Key = models.TextField(blank=True, null=True)
    SUrl = models.TextField(blank=True, null=True)


class NotificationType(models.Model):
    NotificationName = models.CharField(max_length=100, blank=True, null=True)
    STATUS_CHOICES = [(0, "inactive"), (1, "active")]
    CATEGORY = [("N", "Notification"), ("E", "Email")]
    ActiveStatus = models.IntegerField(
        choices=STATUS_CHOICES,
        default=1,
    )

    CategoryType = models.CharField(
        max_length=2,
        choices=CATEGORY,
        default="N",
    )


class PushNotifications(models.Model):
    # NotificationId=models.ForeignKey(FCMDevice, on_delete=models.CASCADE)
    UserId = models.IntegerField(blank=True, null=True)
    Title = models.CharField(max_length=100, blank=True, null=True)
    # Body = models.CharField(max_length=100, blank=True, null=True)
    Body = models.TextField(blank=True, null=True)
    Link = models.TextField(blank=True, null=True)
    NotificationTime = models.DateTimeField(default=timezone.now)
    STATUS_CHOICES = [(0, "read"), (1, "unread")]
    Status = models.IntegerField(
        choices=STATUS_CHOICES,
        default=1,
    )


class PatientPayment(models.Model):
    PAYMENT_STATUS_CHOICES = [
        ('REQUIRES_PAYMENT_METHOD', 'Requires Payment Method'),
        ('REQUIRES_CONFIRMATION', 'Requires Confirmation'),
        ('REQUIRES_ACTION', 'Requires Action'),
        ('PROCESSING', 'Processing'),
        ('SUCCEEDED', 'Succeeded'),
        ('FAILED', 'Failed'),
        ('CANCELED', 'Canceled'),
    ]

    # ... existing fields ...
    payment_status = models.CharField(
        max_length=25,
        choices=PAYMENT_STATUS_CHOICES,
        default='PROCESSING'
    )
    raw_status = models.CharField(max_length=50, blank=True, null=True)
    AppointmentId = models.ForeignKey(Appointments, on_delete=models.CASCADE)
    PaymentIntent = models.CharField(max_length=100, blank=True, null=True)
    PatientId = models.ForeignKey(CuUser, on_delete=models.CASCADE)
    CheckOutSessionId = models.TextField(blank=True, null=True)
    amount = models.IntegerField(default=0)
    currency = models.CharField(max_length=3, default='USD')
    payment_date = models.DateTimeField(default=timezone.now)
    # payment_status = models.CharField(max_length=20)


class RefundedPayments(models.Model):
    PaymentIntent = models.CharField(max_length=100, blank=True, null=True)
    RefundId = models.CharField(max_length=100, blank=True, null=True)
    RefundObject = models.JSONField(blank=True, null=True)
    AmountRefunded = models.IntegerField(blank=True, null=True)
    RefundStatus = models.CharField(max_length=15, blank=True, null=True)
    RefundDate = models.DateTimeField(default=timezone.now)
    RefundCurrency = models.CharField(max_length=10, blank=True, null=True)
    AppId = models.ForeignKey(Appointments, on_delete=models.CASCADE)

# added


class AdminDetails(models.Model):
    AdminId = models.OneToOneField(
        CuUser,
        on_delete=models.CASCADE,
        primary_key=True,
    )
    Address = models.CharField(max_length=100, blank=True, null=True)
    Designation = models.CharField(max_length=150, blank=True, null=True)
    ProfilePhoto = models.TextField(blank=True, null=True)
    PhoneVerified = models.BooleanField(default=False)


class AdminContentManagement(models.Model):
    Content = models.JSONField(null=False)
    Category = models.CharField(max_length=150, null=False)


# added


class ExpertWallet(models.Model):
    ExpertId = models.ForeignKey(CuUser, on_delete=models.CASCADE)


class ExpertWalletTransactions(models.Model):
    TransactionDate = models.DateTimeField(default=timezone.now)
    # ClearedDate = models.DateTimeField(default=timezone.now)
    ClearedDate = models.DateTimeField(null=True, blank=True)
    BalanceAmount = models.IntegerField(default=0)
    TYPE_CHOICES = [(0, "credit"), (1, "debit"), (2, "not paid")]
    TransactionType = models.IntegerField(choices=TYPE_CHOICES, null=True)
    TransactionAmount = models.IntegerField(default=0)
    CommissionAmount = models.IntegerField(default=0)
    WalletId = models.ForeignKey(ExpertWallet, on_delete=models.CASCADE)
    STATUS_CHOICES = [(0, "pending"), (1, "cleared")]
    PaymentStatus = models.IntegerField(choices=STATUS_CHOICES, null=True)
    Invoice = models.TextField(null=True, blank=True)

    appointment = models.ForeignKey(
        Appointments, on_delete=models.CASCADE, null=True, blank=True)

    def save(self, *args, **kwargs):
        # Automatically set ClearedDate if status is set to 'cleared'
        if self.PaymentStatus == 1 and not self.ClearedDate:
            self.ClearedDate = timezone.now()
        super().save(*args, **kwargs)


# added the FAQs model
class FAQs(models.Model):
    question = models.TextField(null=False)
    answer = models.TextField(null=False)


# added blog and podcast category models
class BlogCategory(models.Model):
    Category = models.CharField(max_length=50)
    Description = models.TextField(null=True, blank=True)


class PodcastCategory(models.Model):
    Category = models.CharField(max_length=50)
    Description = models.TextField(blank=True, null=True)


class BlogSection(models.Model):
    SectionName = models.CharField(max_length=50)


class PodcastSection(models.Model):
    SectionName = models.CharField(max_length=50)


# ----------update banner ---------------------
class UpdateBanner(models.Model):
    updates = models.TextField(null=True, blank=True)
    updateTime = models.TextField(null=True, blank=True)
    addedTime = models.DateTimeField(default=timezone.now)


class VideosLibrary(models.Model):
    video_title = models.TextField(blank=False, null=False)
    video_file = models.TextField(blank=True, null=True)
    thumbnail_image = models.TextField(blank=True, null=True)
    isUrl = models.CharField(max_length=50, blank=True, null=True)
    uploadedAt = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.video_title


class UserSubscription(models.Model):
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(
        max_length=15,  # Adjust the max length as needed
        blank=True,
        null=True,
        validators=[
            RegexValidator(
                # Regex for international phone numbers
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
            )
        ]
    )
    c_code = models.CharField(max_length=5, blank=True, null=True)
    is_subscribed = models.BooleanField(default=True)
    unsubscribe_reason = models.TextField(blank=True, null=True)
    subscribed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

