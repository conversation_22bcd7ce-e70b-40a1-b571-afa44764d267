# Generated by Django 4.2.5 on 2025-01-27 12:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0187_alter_videoslibrary_video_file'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('interests', models.Char<PERSON>ield(blank=True, max_length=500, null=True)),
                ('is_subscribed', models.BooleanField(default=False)),
                ('subscribed_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
