from django.db import models
from django.contrib.auth.models import Base<PERSON>ser<PERSON>ana<PERSON>, AbstractBaseUser,PermissionsMixin
from datetime import *
from rest_framework import generics,status
from rest_framework.response import Response
from django.http import JsonResponse
from .patient_models import CuUser
from django.utils import timezone
from .misc_models import PodcastSection, PodcastCategory, BlogCategory, BlogSection

class DoctorDetails(models.Model):
    DoctorId=models.OneToOneField(
        CuUser,
        on_delete=models.CASCADE,
        primary_key=True,
    )
    Address=models.TextField(blank=True, null=True)
    DateOfActivation = models.DateTimeField(null=True)
    Languages=models.CharField(max_length=50, blank=True, null=True)
    Summary=models.TextField()
    PractisingHospital=models.CharField(max_length=50, blank=True, null=True)
    MemberCode=models.CharField(max_length=50, blank=True, null=True)
    Dept=models.Char<PERSON><PERSON>(max_length=50, blank=True, null=True)
    Qualifications=models.JSONField(null=True)
    Certificates=models.JSONField(null=True)
    #TimeZone=models.CharField(max_length=100, blank=True, null=True)
    SocialLinks=models.JSONField(null=True)
    Experience=models.CharField(max_length=20, blank=True, null=True)
    ProfilePhoto = models.TextField(blank=True, null=True)
    EmailVerifyCode = models.CharField(max_length=100, blank=True, null=True)
    EmailVerified = models.BooleanField(default=False)
    EmailCodeGentime = models.DateTimeField(auto_now_add=True)
    IntVideoUrl = models.JSONField(blank=True, null=True)
    ConsultationFees=models.IntegerField(null=True,blank=True)
    NDAConsent = models.IntegerField(default=0)
    Signature = models.TextField(blank=True, null=True)
    Reason=models.JSONField(null=True)
    ReasonDate=models.DateTimeField(default=timezone.now)
    IntroVideoStatus = models.IntegerField(default=1)
    ExperienceSummary = models.JSONField(null=True,blank=True)
    ResearchPapers= models.JSONField(null=True,blank=True)
    SocialLogin = models.IntegerField(default=0)
    CommissionPercentage = models.IntegerField(default=0)
    OtherAchievements= models.JSONField(null=True,blank=True)

    # embedding = models.JSONField(null=True, blank=True)


class DoctorConsent(models.Model):
    DoctorId = models.OneToOneField(
        CuUser,
        on_delete=models.CASCADE,
        primary_key=True,
    )
    ConsentContent=models.TextField(blank=True, null=True)
    STATUS_CHOICES = [
        (0, "rejected"),
        (1, "approved"),
        (2, "pending"),
        (3,"approval_requested"),
    ]
    Status=models.IntegerField(
        choices=STATUS_CHOICES,
        default=2,
    )
    DateOfConsentForm=models.DateTimeField(null=True)

class StatusReason(models.Model):
    ExpertId=models.ForeignKey(CuUser, blank=True, null=True, on_delete=models.CASCADE)
    ReasonCategory= models.CharField(max_length=300, blank=True, null=True)
    ReasonType = models.CharField(max_length=300, blank=True, null=True)
    Reason=models.TextField(blank=True, null=True)
    CurrentTime=models.DateTimeField(auto_now_add=True)

class testjson(models.Model):
    test_data = models.JSONField(null=True)

class ExpertFeedback(models.Model):
    ExpertId=models.ForeignKey(CuUser, blank=True, null=True, on_delete=models.CASCADE)
    FeedbackCategory= models.CharField(max_length=100, blank=True, null=True)
    Feedback = models.TextField(blank=True, null=True)
    CurrentTime=models.DateTimeField(auto_now_add=True)
    status=models.IntegerField(default=2)
    Rating=models.IntegerField(blank=True, null=True)
    
class DoctorReviews(models.Model):
    ExpertId = models.ForeignKey(CuUser, blank=True, null=True, on_delete=models.CASCADE)
    #PatientEmail = models.CharField(max_length=200, blank=True, null=True)
    PatientEmail=models.JSONField(null=True,blank=True)
    Review = models.TextField(blank=True, null=True)
    REVIEW_STATUS_CHOICES = [

        (1, "requested"),
        (2, "approved"),
        (3, "rejected"),
        (0, "pending"),
        (10, "deleted")
    ]
    ReviewStatus = models.IntegerField(
        choices=REVIEW_STATUS_CHOICES,
        default=0,
    )
    ReviewCode=models.CharField(max_length=200, blank=True, null=True)
    ReviewLinkStatus=models.IntegerField(default=1)
    ReviewGenTime = models.DateTimeField(auto_now_add=True)
    ReviewRating=models.IntegerField(default=1)

class Podcast(models.Model):
    ExpertId = models.ForeignKey(CuUser, blank=True, null=True, on_delete=models.CASCADE)
    Platforms=models.JSONField(blank=True, null=True)
    PodcastTopic=models.TextField(blank=True, null=True)
    PODCAST_STATUS_CHOICES = [

        (1, "requested"),
        (2, "published"),
        (3, "rejected"),
        (10, "deleted")
    ]
    PodcastStatus = models.IntegerField(
        choices=PODCAST_STATUS_CHOICES,
        default=1,
    )
    PodcastDate = models.DateTimeField(default=timezone.now)
    ThumbnailImage=models.TextField(blank=True, null=True)
    PodcastViews = models.IntegerField(default=0)
    PodcastCategoryVal = models.ForeignKey(PodcastCategory,max_length=50, null=True,blank=True,on_delete=models.SET_NULL)
    PodcastDescription = models.TextField(null=True,blank=True)
    PodcastRanking=models.IntegerField(blank=True, null=True)
    PodcastSectionVal=models.ForeignKey(PodcastSection,max_length=50, blank=True, null=True,on_delete=models.SET_NULL)#foreign key
    PodcastTranscript=models.TextField(blank=True, null=True)
    PodcastTimestamp=models.TextField(blank=True, null=True)
    HelpfulLinks=models.JSONField(blank=True, null=True)

class ExpertBlogs(models.Model):
    ExpertId = models.ForeignKey(CuUser, on_delete=models.CASCADE)
    BlogTitle=models.CharField(max_length=100, blank=True, null=True)
    BlogBody=models.TextField(blank=True, null=True)
    BlogBannerImage=models.TextField(blank=True, null=True)
    BlogFeatureImage=models.TextField(blank=True, null=True)
    BlogSummary=models.CharField(max_length=500,blank=True, null=True)
    BlogDateTime=models.DateTimeField(auto_now_add=True)
    BlogImages=models.JSONField(blank=True, null=True)
    BlogStatus=models.IntegerField(blank=True, null=True,default=0)
    BlogViews=models.IntegerField(default=0)
    BlogCategoryVal=models.ForeignKey(BlogCategory,blank=True, null=True,on_delete=models.SET_NULL)#foreign key
    BlogSubTitle=models.CharField(max_length=100,blank=True, null=True) #not being used rn
    BlogSubBody=models.TextField(blank=True, null=True) #not being used rn
    BlogSubImage=models.JSONField(blank=True, null=True) #not being used rn
    BlogRanking=models.IntegerField(blank=True, null=True)
    BlogSectionVal=models.ForeignKey(BlogSection, blank=True, null=True,on_delete=models.SET_NULL)#foreign key


#-----------expert ranking------------
class ExpertRank(models.Model):
    rank=models.IntegerField(blank=True, null=True)
    ExpertId=models.ForeignKey(CuUser, blank=True, null=True, on_delete=models.CASCADE)
    
#--------------random expert selection--------------------
class RandomExpert(models.Model):
    ExpertId=models.ForeignKey(CuUser, blank=True, null=True, on_delete=models.CASCADE)    
    
#--------------addition of common topics--------------------
class CommonTopics(models.Model):
    common_topics=models.TextField(blank=True, null=True)
    
#-------------testimonial and review selection-------------------
class ContentSelection(models.Model):
    ExpertId = models.ForeignKey(CuUser, blank=True, null=True, on_delete=models.CASCADE)
    rank = models.BooleanField(null=True, blank=True)
    category = models.TextField(blank=True, null=True)
    c_id= models.IntegerField(blank=True, null=True)
    
    
class ExpertBankDetails(models.Model):
    bank_details = models.JSONField(blank=True , null= True)
    account_holder = models.ForeignKey(CuUser, on_delete=models.CASCADE, blank=True, null=True)


class MedicalSpecialty(models.Model):
    name = models.CharField(max_length=255, unique=True)  # e.g., "Pulmonologist", "Hepatologist"
    # terms = models.JSONField(default=list)  # Stores related terms like ["asthma", "lung issues"]
    embedding = models.JSONField(null=True, blank=True)

    def __str__(self):
        return self.name
    


