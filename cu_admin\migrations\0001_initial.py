# Generated by Django 4.2.5 on 2023-12-16 10:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TimezoneTestModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date1', models.DateTimeField(blank=True, null=True)),
                ('date2', models.DateTimeField(blank=True, null=True)),
            ],
        ),
    ]
