# Generated by Django 4.2.5 on 2024-08-01 09:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0148_expertblogs_blogbannerimage_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlogSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('SectionName', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='PodcastSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('SectionName', models.CharField(max_length=50)),
            ],
        ),
        migrations.RemoveField(
            model_name='expertblogs',
            name='BlogCategory',
        ),
        migrations.Remove<PERSON>ield(
            model_name='podcast',
            name='PodcastCategory',
        ),
        migrations.AddField(
            model_name='expertblogs',
            name='BlogCategoryVal',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='cu_app.blogcategory'),
        ),
        migrations.AddField(
            model_name='expertblogs',
            name='BlogRanking',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='expertwallettransactions',
            name='Invoice',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='podcast',
            name='PodcastCategoryVal',
            field=models.ForeignKey(blank=True, max_length=50, null=True, on_delete=django.db.models.deletion.SET_NULL, to='cu_app.podcastcategory'),
        ),
        migrations.AddField(
            model_name='podcast',
            name='PodcastRanking',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='expertblogs',
            name='BlogSectionVal',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='cu_app.blogsection'),
        ),
        migrations.AddField(
            model_name='podcast',
            name='PodcastSectionVal',
            field=models.ForeignKey(blank=True, max_length=50, null=True, on_delete=django.db.models.deletion.SET_NULL, to='cu_app.podcastsection'),
        ),
    ]
