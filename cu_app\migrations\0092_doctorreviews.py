# Generated by Django 4.2.5 on 2024-02-26 12:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0091_doctordetails_researchpapers'),
    ]

    operations = [
        migrations.CreateModel(
            name='DoctorReviews',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('PatientEmail', models.CharField(blank=True, max_length=200, null=True)),
                ('Review', models.TextField(blank=True, null=True)),
                ('ReviewCode', models.CharField(blank=True, max_length=200, null=True)),
                ('ReviewLinkStatus', models.IntegerField(default=1)),
                ('ReviewGenTime', models.DateTimeField(auto_now_add=True)),
                ('ExpertId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
