# Generated by Django 4.2.5 on 2025-02-03 06:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0191_alter_usersubscription_is_subscribed'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='expertbankdetails',
            name='account_number',
        ),
        migrations.RemoveField(
            model_name='expertbankdetails',
            name='bank_ifsc',
        ),
        migrations.RemoveField(
            model_name='expertbankdetails',
            name='bank_name',
        ),
        migrations.AddField(
            model_name='expertbankdetails',
            name='bank_details',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='expertbankdetails',
            name='account_holder',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
