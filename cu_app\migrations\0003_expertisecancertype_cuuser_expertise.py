# Generated by Django 4.2.5 on 2023-10-04 12:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0002_cu_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExpertiseCancertype',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('type', models.CharField(blank=True, max_length=50, null=True)),
            ],
        ),
        migrations.AddField(
            model_name='cuuser',
            name='expertise',
            field=models.ManyToManyField(to='cu_app.expertisecancertype'),
        ),
    ]
