# Generated by Django 4.2.5 on 2023-12-29 08:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0048_doctordetails_consultationfees'),
    ]

    operations = [
        migrations.AddField(
            model_name='doctordetails',
            name='NDAConsent',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='patientdetails',
            name='NDAConsent',
            field=models.IntegerField(default=0),
        ),
        migrations.CreateModel(
            name='StatusReason',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Reason', models.TextField(blank=True, null=True)),
                ('CurrentTime', models.DateTimeField(auto_now_add=True)),
                ('ExpertId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
