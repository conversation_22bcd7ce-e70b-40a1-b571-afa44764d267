# Generated by Django 4.2.5 on 2023-11-06 14:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0016_doctordetails_experience'),
    ]

    operations = [
        migrations.CreateModel(
            name='Prescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('CurrentDiagnosis', models.CharField(blank=True, max_length=100, null=True)),
                ('ExistingTreatment', models.TextField(blank=True, null=True)),
                ('Recommendation', models.TextField(blank=True, null=True)),
                ('SpecialInstructions', models.TextField(blank=True, null=True)),
                ('FollowUp', models.CharField(blank=True, max_length=100, null=True)),
                ('DoctorSignature', models.CharField(blank=True, max_length=100, null=True)),
                ('AppointmentId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
            ],
        ),
        migrations.CreateModel(
            name='OralMedication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('MedicineName', models.CharField(blank=True, max_length=100, null=True)),
                ('DoseStrength', models.CharField(blank=True, max_length=100, null=True)),
                ('Frequency', models.CharField(blank=True, max_length=100, null=True)),
                ('Duration', models.CharField(blank=True, max_length=100, null=True)),
                ('Remarks', models.CharField(blank=True, max_length=100, null=True)),
                ('PrescriptionId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.prescription')),
            ],
        ),
        migrations.CreateModel(
            name='IVIMMedication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('MedicineName', models.CharField(blank=True, max_length=100, null=True)),
                ('DoseStrength', models.CharField(blank=True, max_length=100, null=True)),
                ('ModeOfAdministration', models.CharField(blank=True, max_length=100, null=True)),
                ('Frequency', models.CharField(blank=True, max_length=100, null=True)),
                ('Remarks', models.CharField(blank=True, max_length=100, null=True)),
                ('PrescriptionId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.prescription')),
            ],
        ),
    ]
