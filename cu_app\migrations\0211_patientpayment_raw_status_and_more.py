# Generated by Django 4.2.5 on 2025-05-29 07:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0210_alter_expertwallettransactions_cleareddate'),
    ]

    operations = [
        migrations.AddField(
            model_name='patientpayment',
            name='raw_status',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='patientpayment',
            name='payment_status',
            field=models.CharField(choices=[('REQUIRES_PAYMENT_METHOD', 'Requires Payment Method'), ('REQUIRES_CONFIRMATION', 'Requires Confirmation'), ('REQUIRES_ACTION', 'Requires Action'), ('PROCESSING', 'Processing'), ('SUCCEEDED', 'Succeeded'), ('FAILED', 'Failed'), ('CANCELED', 'Canceled')], default='PROCESSING', max_length=25),
        ),
    ]
