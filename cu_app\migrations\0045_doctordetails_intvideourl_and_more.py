# Generated by Django 4.2.5 on 2023-12-21 12:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0044_remove_doctordetails_timezone_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='doctordetails',
            name='IntVideoUrl',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='doctordetails',
            name='EmailCodeGentime',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AlterField(
            model_name='schedulerslots',
            name='timezone',
            field=models.CharField(default='UTC', max_length=50),
        ),
    ]
