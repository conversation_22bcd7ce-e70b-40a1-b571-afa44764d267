# Generated by Django 4.2.5 on 2023-11-21 13:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0028_remove_cuuser_user_login_time'),
    ]

    operations = [
        migrations.CreateModel(
            name='MeetingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('SessionId', models.CharField(blank=True, max_length=100, null=True)),
                ('MeetingId', models.CharField(blank=True, max_length=100, null=True)),
                ('MeetingPwd', models.CharField(blank=True, max_length=100, null=True)),
                ('IsDoctorPresent', models.BooleanField(default=False)),
                ('IsPatientPresent', models.BooleanField(default=False)),
                ('SessionStartTime', models.DateTimeField(blank=True, null=True)),
                ('MeetingStatus', models.IntegerField(choices=[(0, 'not started'), (1, 'ongoing'), (2, 'completed')], default=0, max_length=2)),
                ('IsSuccess', models.IntegerField(choices=[(0, 'failed'), (1, 'sucess'), (2, 'not sure')], default=2, max_length=2)),
                ('AppointmentId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
            ],
        ),
    ]
