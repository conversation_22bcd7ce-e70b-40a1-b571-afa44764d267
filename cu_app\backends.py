from django.conf import settings
# from django.contrib.auth.backends import Base<PERSON>ackend
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import check_password
from django.contrib.auth.models import User


class SettingsBackend(ModelBackend):
    """
    Authenticate against the settings ADMIN_LOGIN and ADMIN_PASSWORD.

    Use the login name and a hash of the password
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        print(f"in custom backend----{request}---{type(request)}----{kwargs}----{username}-----{password}")

        UserModel = get_user_model()
        try:
            user = UserModel.objects.get(email=kwargs['email'])
            print(user)
            u_grp=user.groups.all()[0].name
            print(u_grp)

        except:
            print("this is in exception")
            return None
        else:
            # if kwargs['user_role']=='admin':
            #     print(f"admin   approval-------{user.approval}")
            #     if user.check_password(password) and u_grp==kwargs['user_role'] and user.approval=="Approved" and user.is_active==1 and user.is_admin==1:
            #         return user
            # elif kwargs['user_role'] in ['patient','doctor','researcher','influencer']:
            #     print(f" pppp dddd approval-------{user.approval}")
            #     #if user.check_password(password) and u_grp == kwargs['user_role'] and user.approval in ["Approved","Deactivated","pending","Approval_requested","Rejected"] and user.is_active==1:
            #     if user.check_password(password) and user.approval in ["Approved","Deactivated","pending","Approval_requested","Rejected"] and user.is_active==1:
            #         return user
            # elif kwargs['user_role']=='child_admin':
            #     print(f"child admin   approval-------{user.approval}")
            #     if user.check_password(password) and u_grp==kwargs['user_role'] and user.approval=="Approved" and user.is_active==1:
            #         return user
            # else:
            #     pass
                print("this is in else part")
                if kwargs['user_app']=='patient':
                    print(f"patient   approval-------{user.approval}")
                    print(user.is_active,user.check_password(password))
                    if user.check_password(password) and u_grp=="patient" and user.approval in ["Approved","Deactivated","Approval_requested"] and user.is_active==1:
                        return user

                elif kwargs['user_app'] =='expert':
                    print(f" pppp dddd approval-------{user.approval}")
                    if user.check_password(password) and u_grp in ['doctor','researcher','influencer'] and user.approval in ["Approved","Deactivated","pending","Approval_requested","Rejected"] and user.is_active==1:
                        return user

                elif kwargs['user_app']=='admin':
                    print(f"child admin   approval-------{user.approval}")
                    if u_grp =='admin':
                        if user.check_password(password) and u_grp in ['admin','child_admin'] and user.approval=="Approved" and user.is_active==1 and user.is_admin==1:
                            return user
                    elif u_grp=='child_admin':
                        if user.check_password(password) and u_grp in ['admin','child_admin'] and user.approval=="Approved" and user.is_active==1:
                            return user
                    else:
                        pass


                else:
                    pass



        return None

    def get_user(self, user_id):
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None