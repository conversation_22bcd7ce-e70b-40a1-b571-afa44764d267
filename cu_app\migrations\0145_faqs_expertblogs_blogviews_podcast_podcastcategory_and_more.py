# Generated by Django 4.2.5 on 2024-07-24 05:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0144_alter_expertwallet_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='FAQs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.TextField()),
                ('answer', models.TextField()),
            ],
        ),
        migrations.AddField(
            model_name='expertblogs',
            name='BlogViews',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='podcast',
            name='PodcastCategory',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='podcast',
            name='PodcastDescription',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='podcast',
            name='PodcastViews',
            field=models.Integer<PERSON>ield(default=0),
        ),
    ]
