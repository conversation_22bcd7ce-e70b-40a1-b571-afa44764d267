# Generated by Django 4.2.5 on 2024-07-24 10:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0146_expertblogs_blogcategory_expertblogs_blogsubbody_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlogCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Category', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='PodcastCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Category', models.CharField(max_length=50)),
            ],
        ),
    ]
