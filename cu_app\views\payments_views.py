from __future__ import print_function
from rest_framework.decorators import api_view
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from django.http import HttpResponse
from django.http import JsonResponse
from django.shortcuts import render
from rest_framework import generics, status, views
from django.middleware.csrf import get_token
from django.http import JsonResponse, HttpResponse
from cu_app.utils.helper_functions import *
from cu_app.services.airwallex import AirwallexService
from cu_app.utils.pdf_utils import generate_invoice_pdf
from cu_app.utils.helper_functions import calculate_fees
from ..serializers import *
from datetime import *
from rest_framework.response import Response
import json
from django.contrib.auth.models import Group, Permission
from django.views import View
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import authenticate, login, logout, get_user_model
from ..models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import datetime
import os.path
from django.forms.models import model_to_dict

import re
# from datetime import datetime,date, timedelta,timezone
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from ..forms import *
from ..cu_library import *
# aws sns
import logging
import time
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from rest_framework.parsers import JSONParser
import json
import requests
from django.utils import timezone
import zoneinfo
from django.utils.dateparse import parse_datetime
import stripe
import base64
from django.shortcuts import redirect
from django.core.paginator import Paginator
import calendar
from django.shortcuts import get_object_or_404

# AIRWALLEX PAYMENT VIEW


class PaymentsViewFromAirwallex(views.APIView):
    def get(self, request):
        try:
            # Get patient's email (adjust based on your user model)
            # Or request.query_params.get('email')
            patient_email = request.user.email

            # Fetch from Airwallex
            airwallex = AirwallexService()
            payments = airwallex.get_payments_by_customer(patient_email)

            # Filter and format the response
            formatted_payments = []
            for payment in payments.get('items', []):
                formatted_payments.append({
                    'id': payment['id'],
                    'amount': payment['amount'],
                    'currency': payment['currency'],
                    'status': payment['status'],
                    'created_at': payment['created_at'],
                    'metadata': payment.get('metadata', {})
                })
            print("formatted_payments", formatted_payments)

            return Response({
                'count': len(formatted_payments),
                'results': formatted_payments
            })

        except Exception as e:
            return Response(
                {"error": f"Failed to fetch payments: {str(e)}"},
                status=400
            )


class PatientPaymentListView(views.APIView):
    serializer_class = PatientPaymentsSerializer
    permission_classes = []

    def get(self, request, id):
        # Get pagination parameters
        page_number = request.GET.get('page', 1)
        items_per_page = request.GET.get('per_page', 10)

        airwallex = AirwallexService()

        def update_payment_status(payment):
            try:
                payment_intent = airwallex.get_payment_intent(
                    payment.PaymentIntent)
                latest_status = payment_intent.get('status')
                if latest_status and latest_status != payment.payment_status:
                    payment.payment_status = latest_status
                    payment.save(update_fields=['payment_status'])
            except Exception:
                # silently ignore errors
                pass

        if get_cu_user_type(id) == "patient":
            # Get payments for patient
            patient_payments = PatientPayment.objects.filter(PatientId=id)
            total_items = patient_payments.count()

            # Apply filters if provided
            if 'name' in request.GET and request.GET['name'] != '':
                patient_payments = patient_payments.filter(
                    AppointmentId__slot_id__doctor__name__icontains=request.GET['name']
                )

            if 'start_date' in request.GET and request.GET['start_date'] != '':
                start_date = timezone.make_aware(
                    parse_datetime(request.GET['start_date']),
                    timezone.get_current_timezone()
                )
                patient_payments = patient_payments.filter(
                    payment_date__gte=start_date
                )

            if 'end_date' in request.GET and request.GET['end_date'] != '':
                end_date = timezone.make_aware(
                    parse_datetime(request.GET['end_date']),
                    timezone.get_current_timezone()
                )
                patient_payments = patient_payments.filter(
                    payment_date__lte=end_date
                )

            # Process payments
            paginator = Paginator(patient_payments, items_per_page)

            if int(page_number) not in range(1, paginator.num_pages + 1):
                return JsonResponse({"error": "Not a valid page number"}, status=400)

            current_page = paginator.page(page_number)

            # Build response data (without Airwallex API call)
            payment_details = []
            for payment in current_page:
                update_payment_status(payment)

                payment_details.append({
                    "id": payment.id,
                    "payment_id": payment.PaymentIntent,  # Just the ID, no Airwallex lookup
                    "date": timezone.localtime(payment.payment_date),
                    # "date": payment.payment_date,
                    "amount": payment.amount,
                    "currency": payment.currency,
                    "patient_name": payment.AppointmentId.patient.name,
                    "patient_email": payment.AppointmentId.patient.email,
                    "status": payment.payment_status,
                    "AppointmentId": payment.AppointmentId.id,
                    "doctor": payment.AppointmentId.slot_id.doctor.name,
                    "doctor_prefix": payment.AppointmentId.slot_id.doctor.prefix,
                    "doctor_member_code": DoctorDetails.objects.get(
                        DoctorId_id=payment.AppointmentId.slot_id.doctor.id
                    ).MemberCode
                })

            # payment_details_master = []
            # for payment in current_page:
            #     print("payment in the current page ",payment)
            #     try:
            #         # Get payment intent details from Airwallex
            #         payment_intent = airwallex.get_payment_intent(
            #             payment.PaymentIntent)

            #         print("this is the payment intents in a loop",payment_intent)

            #         payment_data = {
            #             "payment_id": payment_intent['id'],
            #             "date": payment.payment_date,
            #             "payment_status": payment_intent['status'],
            #             "currency": payment_intent['currency'],
            #             "amount": payment_intent['amount'],
            #             "AppointmentId": payment.AppointmentId.id,
            #             "doctor": payment.AppointmentId.slot_id.doctor.name,
            #             "doctor_prefix": payment.AppointmentId.slot_id.doctor.prefix,
            #             "doctor_member_code": DoctorDetails.objects.get(
            #                 DoctorId_id=payment.AppointmentId.slot_id.doctor.id
            #             ).MemberCode
            #         }
            #         payment_details_master.append(payment_data)
            #         print("payment_details_master after the payment intents",payment_details_master)

            #     except Exception as e:
            #         print(f"Error processing payment {payment.id}: {str(e)}")
            #         continue

            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': payment_details
            }

            return JsonResponse(response_data)

        elif get_cu_user_type(id) in ['doctor', 'researcher', 'influencer']:
            # Get payments for doctor's appointments
            doctor_slots = SchedulerSlots.objects.filter(doctor_id=id)
            appointments = Appointments.objects.filter(
                slot_id__in=doctor_slots)

            total_items = appointments.count()

            # Process payments
            payment_details_master = []
            paginator = Paginator(appointments, items_per_page)

            if int(page_number) not in range(1, paginator.num_pages + 1):
                return JsonResponse({"error": "Not a valid page number"}, status=400)

            current_page = paginator.page(page_number)
            for appointment in current_page:
                try:
                    payment = PatientPayment.objects.filter(
                        AppointmentId=appointment.id
                    ).select_related('AppointmentId__patient').first()  # Optimized query

                    if not payment:
                        continue
                    update_payment_status(payment)

                    # Use direct database fields instead of Airwallex API call
                    payment_data = {
                         "id": payment.id,
                        "payment_id": payment.PaymentIntent,  # Store this during initial payment
                        "date": payment.payment_date,
                        "payment_status": payment.payment_status,  # Should store in your model
                        "AppointmentId": appointment.id,
                        "patient_name": appointment.patient.name,
                        "patient_prefix": appointment.patient.prefix,
                        "currency": payment.currency,  # Store this in PatientPayment
                        "amount": payment.amount,  # Store this in PatientPayment
                        "patient_email": payment.AppointmentId.patient.email,
                    }
                    payment_details_master.append(payment_data)

                except Exception as e:
                    print(
                        f"Error processing appointment {appointment.id}: {str(e)}")
                    continue  # Skip failed records but continue processing others

            # for appointment in current_page:
            #     try:
            #         payment = PatientPayment.objects.filter(
            #             AppointmentId=appointment.id).first()
            #         if not payment:
            #             continue

            #         payment_intent = airwallex.get_payment_intent(
            #             payment.PaymentIntent)

            #         payment_data = {
            #             "payment_id": payment_intent['id'],
            #             "date": payment.payment_date,
            #             "payment_status": payment_intent['status'],
            #             "AppointmentId": appointment.id,
            #             "patient_name": appointment.patient.name,
            #             "patient_prefix": appointment.patient.prefix,
            #             "currency": payment_intent['currency'],
            #             "amount": payment_intent['amount']
            #         }
            #         payment_details_master.append(payment_data)

            #     except Exception as e:
            #         print(
            #             f"Error processing appointment {appointment.id}: {str(e)}")
            #         continue

            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': payment_details_master
            }

            return JsonResponse(response_data)

        elif id == 'all':
            # Get all payments (admin view)
            all_payments = PatientPayment.objects.all()
            total_items = all_payments.count()

            # Process payments
            payment_details_master = []
            paginator = Paginator(all_payments, items_per_page)

            if int(page_number) not in range(1, paginator.num_pages + 1):
                return JsonResponse({"error": "Not a valid page number"}, status=400)

            current_page = paginator.page(page_number)

            # for payment in current_page:
            #     try:
            #         payment_intent = airwallex.get_payment_intent(
            #             payment.PaymentIntent)

            #         payment_data = {
            #             "payment_id": payment_intent['id'],
            #             "date": payment.payment_date,
            #             "payment_status": payment_intent['status'],
            #             "currency": payment_intent['currency'],
            #             "amount": payment_intent['amount'],
            #             "AppointmentId": payment.AppointmentId.id,
            #             "Appointment_status": payment.AppointmentId.status,
            #             "doctor": payment.AppointmentId.slot_id.doctor.name,
            #             "doctor_prefix": payment.AppointmentId.slot_id.doctor.prefix,
            #             "doctor_member_code": DoctorDetails.objects.get(
            #                 DoctorId_id=payment.AppointmentId.slot_id.doctor.id
            #             ).MemberCode,
            #             "patient_name": payment.AppointmentId.patient.name,
            #             "patient_prefix": payment.AppointmentId.patient.prefix
            #         }
            #         payment_details_master.append(payment_data)

            #     except Exception as e:
            #         print(f"Error processing payment {payment.id}: {str(e)}")
            #         continue

            for payment in current_page:
                update_payment_status(payment)
                try:
                    doctor_details = DoctorDetails.objects.filter(
                        DoctorId_id=payment.AppointmentId.slot_id.doctor.id
                    ).first()

                    payment_data = {
                         "id": payment.id,
                        "payment_id": payment.PaymentIntent,  # From your database
                        "date": payment.payment_date,
                        "status": payment.payment_status,  # Store this in your model
                        "currency": payment.currency,  # Store this in your model
                        "amount": payment.amount,  # Store this in your model
                        "AppointmentId": payment.AppointmentId.id,
                        "Appointment_status": payment.AppointmentId.status,
                        "doctor": payment.AppointmentId.slot_id.doctor.name,
                        "doctor_prefix": payment.AppointmentId.slot_id.doctor.prefix,
                        "doctor_member_code": doctor_details.MemberCode if doctor_details else None,
                        "patient_name": payment.AppointmentId.patient.name,
                        "patient_prefix": payment.AppointmentId.patient.prefix,
                        "patient_email": payment.AppointmentId.patient.email,
                    }
                    payment_details_master.append(payment_data)

                except Exception as e:
                    print(f"Error processing payment {payment.id}: {str(e)}")
                    continue

            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': payment_details_master
            }

            return JsonResponse(response_data)

        return JsonResponse({"error": "Invalid user type"}, status=400)
# AIRWALLEX PAYMENT VIEW

# DOWNLOAD INVOICE


logger = logging.getLogger(__name__)

# views.py


class DownloadInvoiceView(views.APIView):
    model = PatientPayment
    pk_url_kwarg = 'payment_id'
    permission_classes = []

    def get(self, request, *args, **kwargs):
        payment_id = kwargs.get("id")
        try:
            payment = PatientPayment.objects.get(id=payment_id)

            slot_id = payment.AppointmentId.slot_id_id  # Adjust based on your model
            doctor_id = payment.AppointmentId.slot_id.doctor.id  # Adjust as needed

            # Calculate fees
            fee_details = calculate_fees(doctor_id, slot_id, payment.amount)

            # Prepare data for the invoice
            invoice_data = {
                'appointment_id': str(payment.AppointmentId.id),
                'payment_intent': payment.PaymentIntent or 'N/A',
                'patient_id': str(payment.PatientId.id),
                'patient_name': payment.PatientId.name or 'Unknown Patient',
                # 'checkout_session_id': payment.CheckOutSessionId or 'N/A',
                'amount': payment.amount,
                'currency': payment.currency.upper(),
                'payment_date': payment.payment_date.strftime('%Y-%m-%d %H:%M:%S %Z'),
                'payment_status': payment.payment_status.capitalize(),

                # Add fee details
                'doctor_fees': fee_details["doctor_fees"],
                'platform_charges': fee_details["platform_charges"],
                'transaction_charges': fee_details["transaction_charges"],
                'final_charge': fee_details["final_charge"],
            }

            # Create PDF response
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="invoice_{payment.id}.pdf"'
            # response['Content-Disposition'] = 'inline; filename="invoice_{}.pdf"'

            # Generate PDF
            generate_invoice_pdf(response, invoice_data)

            return response

        except Exception as e:
            logger.error(
                f"Error generating invoice PDF for payment {self.kwargs.get('payment_id')}: {str(e)}")
            return HttpResponse(
                f"Error generating invoice: {str(e)}",
                status=500,
                content_type='text/plain'
            )


class GetPaymentsView(views.APIView):
    serializer_class = PatientPayment

    def get(self, r, id):
        # Get the page number from the request
        page_number = r.GET.get('page', 1)
        # Get the number of items per page from the request
        items_per_page = r.GET.get('per_page', 10)
        if get_cu_user_type(id) == "patient":
            stripe.api_key = os.getenv("STRIPE_API_KEY")
            patient_payment_details = PatientPayment.objects.filter(
                PatientId__exact=id)
            total_items = patient_payment_details.count()
            if 'name' in r.GET and r.GET['name'] != '':
                patient_payment_details = patient_payment_details.filter(
                    AppointmentId__slot_id__doctor__name__icontains=r.GET['name'])
            if 'start_date' in r.GET and r.GET['start_date'] != '':
                start_date = timezone.make_aware(parse_datetime(r.GET['start_date']),
                                                 timezone.get_current_timezone())
                patient_payment_details = patient_payment_details.filter(
                    AppointmentId__DateOfFixingApp__gte=start_date)

            if "end_date" in r.GET and r.GET['end_date'] != '':
                end_date = timezone.make_aware(parse_datetime(r.GET['end_date']),
                                               timezone.get_current_timezone())
                patient_payment_details = patient_payment_details.filter(
                    AppointmentId__DateOfFixingApp__lte=end_date)

            patient_payment_details_master = []
            paginator = Paginator(patient_payment_details, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            patient_payment_details = paginator.page(page_number)
            for x in patient_payment_details:
                a = dict()
                p_int = stripe.PaymentIntent.retrieve(x.PaymentIntent)
                # aa=stripe.checkout.Session.retrieve(x.CheckOutSessionId)
                p_invoice = stripe.Invoice.retrieve(p_int.invoice)
                p_date = timezone.make_aware(datetime.fromtimestamp(
                    p_invoice['created']), timezone.get_current_timezone())
                description = ''
                for y in p_invoice['lines']['data']:
                    description = y['description']
                    break
                a.update({"invoice": {"id": p_invoice['id'], "account_name": p_invoice['account_name'], "created": p_invoice['created'], "customer_email": p_invoice[
                         'customer_email'], "customer_name": p_invoice['customer_name'], "description": description, "invoice_pdf": p_invoice['invoice_pdf']}})
                a.update({"date": p_date})
                a.update({"payment_status": p_int.status})
                a.update({"currency": p_int.currency})
                a.update({"amount": p_int.amount})
                a.update({"AppointmentId": x.AppointmentId.id})
                a.update({"doctor": SchedulerSlots.objects.filter(
                    appointments__exact=x.AppointmentId)[0].doctor.name})
                a.update({"doctor_prefix": SchedulerSlots.objects.filter(
                    appointments__exact=x.AppointmentId)[0].doctor.prefix})
                a.update({"doctor_member_code": DoctorDetails.objects.get(DoctorId_id__exact=SchedulerSlots.objects.filter(
                    appointments__exact=x.AppointmentId)[0].doctor.id).MemberCode})
                patient_payment_details_master.append(a)
            # print(f"in paymentsssssssss-------------{patient_payment_details_master}")
            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': patient_payment_details_master
            }
            return JsonResponse(response_data)

        elif get_cu_user_type(id) in ['doctor', 'researcher', 'influencer']:
            stripe.api_key = os.getenv("STRIPE_API_KEY")
            payment_details_master = []
            doctor_slots_list = SchedulerSlots.objects.filter(
                doctor_id__exact=id)
            slot_ids = doctor_slots_list.values("id")
            app_status = Appointments.objects.filter(slot_id_id__in=slot_ids)
            print(f'Queryset: {app_status}')
            total_items = app_status.count()
            paginator = Paginator(app_status, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            app_status = paginator.page(page_number)
            for x in app_status:
                a = dict()
                payment_details = PatientPayment.objects.filter(
                    AppointmentId_id=x.id)
                if payment_details.exists():
                    payment_details = PatientPayment.objects.filter(
                        AppointmentId_id=x.id)[0]
                    p_int = stripe.PaymentIntent.retrieve(
                        payment_details.PaymentIntent)
                    p_invoice = stripe.Invoice.retrieve(p_int.invoice)
                    p_date = timezone.make_aware(datetime.fromtimestamp(
                        p_invoice['created']), timezone.get_current_timezone())
                    a.update({"invoice": {"status_transitions": {
                             "paid_at": p_invoice['status_transitions']['paid_at']}}})
                    a.update({"payment_status": p_int.status})
                    a.update(
                        {"AppointmentId": payment_details.AppointmentId.id})
                    a.update({"patient_name": app_status[0].patient.name})
                    a.update({"patient_prefix": app_status[0].patient.prefix})
                    payment_details_master.append(a)
                else:

                    print(f"in paymerrrrrrrrrrrrrrrrrrrrrntsssssssss-------------")

            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': payment_details_master
            }
            return JsonResponse(response_data)
        # added
        elif id == 'all':
            stripe.api_key = os.getenv("STRIPE_API_KEY")
            patient_payment_details = PatientPayment.objects.all()
            total_items = patient_payment_details.count()
            patient_payment_details_master = []
            paginator = Paginator(patient_payment_details, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            patient_payment_details = paginator.page(page_number)
            for x in patient_payment_details:
                a = dict()
                p_int = stripe.PaymentIntent.retrieve(x.PaymentIntent)
                # aa=stripe.checkout.Session.retrieve(x.CheckOutSessionId)
                p_invoice = stripe.Invoice.retrieve(p_int.invoice)
                p_date = timezone.make_aware(datetime.fromtimestamp(
                    p_invoice['created']), timezone.get_current_timezone())
                description = ''
                for y in p_invoice['lines']['data']:
                    description = y['description']
                    break
                a.update({"invoice": {"id": p_invoice['id'], "account_name": p_invoice['account_name'], "created": p_invoice['created'], "customer_email": p_invoice[
                         'customer_email'], "customer_name": p_invoice['customer_name'], "description": description, "invoice_pdf": p_invoice['invoice_pdf']}})
                a.update({"date": p_date})
                a.update({"payment_status": p_int.status})
                a.update({"currency": p_int.currency})
                a.update({"amount": p_int.amount})
                a.update({"AppointmentId": x.AppointmentId.id})
                a.update({"Appointment_status": Appointments.objects.filter(
                    id__exact=x.AppointmentId.id)[0].status})
                a.update({"doctor": SchedulerSlots.objects.filter(
                    appointments__exact=x.AppointmentId)[0].doctor.name})
                a.update({"doctor_prefix": SchedulerSlots.objects.filter(
                    appointments__exact=x.AppointmentId)[0].doctor.prefix})
                a.update({"doctor_member_code": DoctorDetails.objects.get(DoctorId_id__exact=SchedulerSlots.objects.filter(
                    appointments__exact=x.AppointmentId)[0].doctor.id).MemberCode})
                patient_payment_details_master.append(a)
            # print(f"in paymentsssssssss-------------{patient_payment_details_master}")
            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': patient_payment_details_master
            }
            return JsonResponse(response_data)

  # previous payments view using the stripe


class GetDoctorPaymentsView(views.APIView):
    serializer_class = PatientPayment

    def get(self, r, id):
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        payment_details_master = []
        doctor_slots_list = SchedulerSlots.objects.filter(doctor_id__exact=id)

        for x in doctor_slots_list:
            a = dict()
            app_status = Appointments.objects.filter(slot_id_id__exact=x.id)
            if app_status.exists():
                payment_details = PatientPayment.objects.filter(
                    AppointmentId_id=app_status[0].id)
                if payment_details.exists():
                    payment_details = PatientPayment.objects.filter(
                        AppointmentId_id=app_status[0].id)[0]
                    p_int = stripe.PaymentIntent.retrieve(
                        payment_details.PaymentIntent)
                    p_invoice = stripe.Invoice.retrieve(p_int.invoice)
                    a.update({"invoice": p_invoice})
                    a.update({"payment_status": p_int.status})
                    a.update(
                        {"AppointmentId": payment_details.AppointmentId.id})
                    a.update({"patient_name": app_status[0].patient.name})
                    payment_details_master.append(a)
                else:
                    pass
            # print(f"in paymentsssssssss-------------{patient_payment_details_master}")

            else:
                pass

        return JsonResponse({"payments_data": payment_details_master})

    # added


# added
class CreateRefundsView(views.APIView):
    serializer_class = PatientPayment

    def post(self, r, AppointmentId_id):
        app_id = AppointmentId_id
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        patient_payment_details = PatientPayment.objects.filter(
            AppointmentId_id__exact=app_id)

        if PatientPayment.objects.filter(AppointmentId_id__exact=app_id).exists():
            patient_payment_details = PatientPayment.objects.filter(
                AppointmentId_id__exact=app_id)[0]

            p_int = stripe.PaymentIntent.retrieve(
                patient_payment_details.PaymentIntent)
            amount = int(p_int.amount)
            # if ['partial_r']:
            if 'partial_r' in r.data:
                p_r = r.data['partial_r']
                if RefundedPayments.objects.filter(PaymentIntent=patient_payment_details.PaymentIntent).exists():
                    actual_amount = amount - RefundedPayments.objects.filter(
                        PaymentIntent=patient_payment_details.PaymentIntent)[0].AmountRefunded
                    amount = int(int(actual_amount) * float(p_r*0.01))
                    r_p = stripe.Refund.create(
                        payment_intent=p_int, amount=amount)
                    print(f"in paymentsssssssss-------------{r_p}")
                else:
                    amount = int(int(amount) * float(p_r*0.01))
                    r_p = stripe.Refund.create(
                        payment_intent=p_int, amount=amount)
                    print(f"in paymentsssssssss-------------{r_p}")

            else:
                r_p = stripe.Refund.create(payment_intent=p_int)
                print(f"in paymentsssssssss-------------{r_p}")
        else:
            r_p = "No payment done yet"

        return JsonResponse({"payments_data": r_p})

# added

# -------------------- added for the revenue ------------------------


class TotalRevenue(views.APIView):

    def get(self, r, *args, **kwargs):
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        m_onth = 1
        d_list = []
        m_list = []
        if self.kwargs['year'] == 'curr':
            year = timezone.now().year
            if self.kwargs['month'] == "all":
                lm = timezone.now().month
                lm = lm+1
                for x in range(1, lm):
                    l1 = dict()
                    s_um = 0
                    print(x)
                    res = calendar.monthrange(year, x)
                    p_m = ''
                    if x < 10:
                        p_m = "0"+str(x)
                    else:
                        p_m = str(x)
                    start_date = timezone.make_aware(parse_datetime(
                        str(year)+"-"+p_m+"-01"), timezone.get_current_timezone())
                    print(start_date)
                    end_date = timezone.make_aware(parse_datetime(
                        str(year)+"-"+p_m+"-"+str(res[1])), timezone.get_current_timezone())
                    print(end_date)
                    stripe_data = stripe.Invoice.list(
                        status="paid",  # Filters invoices to only include those marked as paid
                        created={
                            # Filters invoices created after or at the timestamp stored in res[0]
                            "gte": start_date,
                            # Filters invoices created before or at the timestamp stored in res[1]
                            "lte": end_date
                        })
                    for i in stripe_data['data']:
                        s_um += int(i['amount_paid'])
                    l1.update({str(x): str(s_um)})
                    d_list.append(l1)
            else:
                l1 = dict()
                s_um = 0
                p_m = self.kwargs['month']
                res = calendar.monthrange(year, int(p_m))
                start_date = timezone.make_aware(parse_datetime(
                    str(year)+"-"+p_m+"-01"), timezone.get_current_timezone())
                print(start_date)
                end_date = timezone.make_aware(parse_datetime(
                    str(year)+"-"+p_m+"-"+str(res[1])), timezone.get_current_timezone())
                print(end_date)
                stripe_data = stripe.Invoice.list(
                    status="paid",  # Filters invoices to only include those marked as paid
                    created={
                        # Filters invoices created after or at the timestamp stored in res[0]
                        "gte": start_date,
                        # Filters invoices created before or at the timestamp stored in res[1]
                        "lte": end_date
                    })
                for i in stripe_data['data']:
                    s_um += int(i['amount_paid'])
                l1.update({str(p_m): str(s_um)})
                d_list.append(l1)
        else:
            year = int(self.kwargs.get('year'))
            lm = 12
            if self.kwargs['month'] == "all":
                lm = 12
                for x in range(1, lm+1):
                    l1 = dict()
                    s_um = 0
                    print(x)
                    res = calendar.monthrange(year, x)
                    p_m = ''
                    if x < 10:
                        p_m = "0"+str(x)
                    else:
                        p_m = str(x)
                    start_date = timezone.make_aware(parse_datetime(
                        str(year)+"-"+p_m+"-01"), timezone.get_current_timezone())
                    print(start_date)
                    end_date = timezone.make_aware(parse_datetime(
                        str(year)+"-"+p_m+"-"+str(res[1])), timezone.get_current_timezone())
                    print(end_date)
                    stripe_data = stripe.Invoice.list(
                        status="paid",  # Filters invoices to only include those marked as paid
                        created={
                            # Filters invoices created after or at the timestamp stored in res[0]
                            "gte": start_date,
                            # Filters invoices created before or at the timestamp stored in res[1]
                            "lte": end_date
                        })
                    for i in stripe_data['data']:
                        s_um += int(i['amount_paid'])
                    l1.update({str(x): str(s_um)})
                    d_list.append(l1)
            else:
                l1 = dict()
                s_um = 0
                p_m = self.kwargs['month']
                res = calendar.monthrange(year, int(p_m))
                start_date = timezone.make_aware(parse_datetime(
                    str(year)+"-"+p_m+"-01"), timezone.get_current_timezone())
                print(start_date)
                end_date = timezone.make_aware(parse_datetime(
                    str(year)+"-"+p_m+"-"+str(res[1])), timezone.get_current_timezone())
                print(end_date)
                stripe_data = stripe.Invoice.list(
                    status="paid",  # Filters invoices to only include those marked as paid
                    created={
                        # Filters invoices created after or at the timestamp stored in res[0]
                        "gte": start_date,
                        # Filters invoices created before or at the timestamp stored in res[1]
                        "lte": end_date
                    })
                for i in stripe_data['data']:
                    s_um += int(i['amount_paid'])
                l1.update({str(p_m): str(s_um)})
                d_list.append(l1)
        return JsonResponse(d_list, safe=False)
