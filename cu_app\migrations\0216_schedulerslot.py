# Generated by Django 4.2.5 on 2025-06-16 07:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0215_meetingsession_expirationtime'),
    ]

    operations = [
        migrations.CreateModel(
            name='SchedulerSlot',
            fields=[
                ('slot_id', models.AutoField(primary_key=True, serialize=False)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('slot_status', models.CharField(max_length=5)),
                ('expert_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scheduler_slots', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
