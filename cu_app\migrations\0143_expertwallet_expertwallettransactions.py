# Generated by Django 4.2.5 on 2024-07-18 06:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0142_podcast_thumbnailimage'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExpertWallet',
            fields=[
                ('id', models.IntegerField(auto_created=True, primary_key=True, serialize=False)),
                ('ExpertId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ExpertWalletTransactions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('TransactionDate', models.DateTimeField(default=django.utils.timezone.now)),
                ('BalanceAmount', models.IntegerField(default=0)),
                ('TransactionType', models.IntegerField(choices=[(0, 'credit'), (1, 'debit')], null=True)),
                ('TransactionAmount', models.IntegerField(default=0)),
                ('PaymentStatus', models.IntegerField(choices=[(0, 'pending'), (1, 'cleared')], null=True)),
                ('WalletId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.expertwallet')),
            ],
        ),
    ]
