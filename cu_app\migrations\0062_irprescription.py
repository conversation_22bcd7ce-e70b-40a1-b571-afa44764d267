# Generated by Django 4.2.5 on 2024-01-20 06:54

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0061_doctordetails_signature'),
    ]

    operations = [
        migrations.CreateModel(
            name='IRPrescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('CurrentDiagnosis', models.CharField(blank=True, max_length=100, null=True)),
                ('ConsultationSummary', models.TextField(blank=True, null=True)),
                ('Remarks', models.TextField(blank=True, null=True)),
                ('SpecialInstructions', models.TextField(blank=True, null=True)),
                ('FollowUp', models.CharField(blank=True, max_length=100, null=True)),
                ('DateOfCreatingPresc', models.DateField(default=datetime.date.today)),
                ('AppointmentId', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
            ],
        ),
    ]
